version: "3"
services:
  ugs-mysql:
    container_name: "ugs-mysql"
    image: *************:808/ugs/mysql:8.0.17_1
    volumes:
      - /clouddata/mysql/cloud_platform/db/:/var/lib/mysql/
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    security_opt:
      - seccomp:unconfined
    env_file:
      - .env
    ports:
      - ${DB_DOCKER_EXPOSE_PORT}:3306
    restart: always
  ugs-rabbitmq:
    container_name: "ugs-rabbitmq"
    image: *************:808/ugs/rabbitmq:3.5.7_1
    env_file:
      - .env
    volumes:
      - /clouddata/rabbitmq/data/:/var/lib/rabbitmq/
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    environment:
      - RABBITMQ_DEFAULT_USER=${MQ_USERNAME}
      - RABBITMQ_DEFAULT_PASS=${MQ_PASSWORD}
    ports:
      - ${MQ_DOCKER_PORT}:15672
      - ${MQ_DOCKER_TCP_PORT}:5672
      - 15678:15670
      - 15679:15674
    hostname: ${MQ_HOSTNAME}
    restart: always
  ugs-pacs:
    container_name: "ugs-pacs"
    image: *************:808/ugs/pacs:1.5.5_2
    ports:
      - ${ORTHANC_DOCKER_PORT}:8042
      - 4242:4242
    env_file:
      - .env
    volumes:
      - ./volume/pacs/orthanc.json:/etc/orthanc/orthanc.json
      - /clouddata/pacs/db-v6/:/var/lib/orthanc/db/
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    restart: always
  ugs-mongodb:
    container_name: "ugs-mongodb"
    image: *************:808/ugs/mongo:4.2.1_1
    env_file:
      - .env
    volumes:
      - /clouddata/mongodb/cloud_platform/db2/:/data/db/
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_INITDB_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}
    ports:
      - ${MONGODB_DOCKER_PORT}:27017
    command: --wiredTigerCacheSizeGB 1.5
    restart: always
  ugs-api:
    container_name: "ugs-api"
    build:
      context: .
      dockerfile: Dockerfile
    image: harbor.unionstrongtech.com/ugs/backend:0.0.5
    ports:
      - ${CLOUD_PLATFORM_API_PORT}:4201
    volumes:
      - ./src/server:/code/server
      - ./volume/uwsgi/dev.ini:/code/uwsgi.ini
      - ./volume/pacs/orthanc.json:${ORTHANC_CONFIG_FILE}
      - /data/ctpdata:/code/data
      - /home/<USER>/upixel-station-backend/server/static/cpr:/code/server/static/cpr
      - /home/<USER>/upixel-station-backend/server/static/jpg:/code/server/static/jpg
      - /home/<USER>/upixel-station-backend/server/static/vti:/code/server/static/vti
      - /clouddata/pacs/db-v6:/clouddata/pacs/db-v6
      - /usr/share/zoneinfo:/usr/share/zoneinfo
      - /usr/sbin/dmidecode:/usr/sbin/dmidecode
      - /dev/mem:/dev/mem
    working_dir: /code/
    privileged: true
    env_file:
      - .env
    links:
      - ugs-mysql
      - ugs-rabbitmq
      - ugs-mongodb
      - ugs-pacs
    depends_on:
      - ugs-mysql
    command: uwsgi --ini uwsgi.ini
    restart: always
  ugs-nginx:
    container_name: "ugs-nginx"
    image: *************:808/ugs/nginx:1.17.3_2
    ports:
      - ${CLOUD_PLATFORM_LOCAL_PORT}:4200
    volumes:
      - /home/<USER>/ugs-frontend:/usr/share/nginx/local-frontend
      - /data/ctpdata:/data/ctpdata
      - /home/<USER>/upixel-station-backend/server/static:/backend/static
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    env_file:
      - .env
    links:
      - ugs-api
    depends_on:
      - ugs-api
    restart: always