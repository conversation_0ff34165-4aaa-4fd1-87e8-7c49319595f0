#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup
@Project : ugs-api
<AUTHOR> mingxing
@Date    : 2023/3/27 13:26
"""
from setuptools import setup, find_packages, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize


ext_modules = [
    Extension("server.*", ["src/server/*.py"]),
    Extension("server.algorithm.*", ["src/server/algorithm/*.py"]),
    Extension("server.api.*", ["src/server/api/*.py"]),
    Extension("server.async.*", ["src/server/async/*.py"]),
    Extension("server.common.*", ["src/server/common/*.py"]),
    Extension("server.ge.*", ["src/server/ge/*.py"]),
    Extension("server.image.*", ["src/server/image/*.py"]),
    Extension("server.mails.*", ["src/server/mails/*.py"]),
    Extension("server.report.*", ["src/server/report/*.py"]),
    Extension("server.series.*", ["src/server/series/*.py"]),
    Extension("server.study.*", ["src/server/study/*.py"]),
    Extension("server.systemconfig.*", ["src/server/systemconfig/*.py"]),
    Extension("server.user.*", ["src/server/user/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name="ugs_api",
    author="unionstrong",
    url="http://************/uguard-stroke/ugs-api.git",
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version="1.8.1",
    packages=["server"],
    package_dir={"": "src"},
    package_data={"server": ["static/img/*.png", "static/tls/*.pem"]},
    platforms="linux_x86_64",
    description="UGS API",
    long_description="UGuard Stroke API",
    license="MIT"
)
