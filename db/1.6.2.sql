-- ----------------------------
-- Database
-- ----------------------------
DROP DATABASE IF EXISTS `algorithm`;
CREATE DATABASE `algorithm` DEFAULT COLLATE utf8mb4_unicode_ci;

DROP DATABASE IF EXISTS `cloud`;
CREATE DATABASE `cloud` DEFAULT COLLATE utf8mb4_unicode_ci;

USE `algorithm`;

-- ----------------------------
-- Table structure for algorithm_callback
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_callback`;
CREATE TABLE `algorithm_callback`
(
    `uuid`             varchar(255) NOT NULL,
    `callback`         tinyint(1) NOT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          varchar(255) NULL DEFAULT NULL,
    <PERSON><PERSON><PERSON><PERSON> KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for algorithm_task
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_task`;
CREATE TABLE `algorithm_task`
(
    `uuid`           varchar(255) NOT NULL,
    `series_uid`     varchar(255) NOT NULL,
    `user_id`        varchar(255) NOT NULL,
    `algorithm_type` varchar(255) NOT NULL,
    `finish_percent` int(11) NOT NULL,
    `error_code`     int(11) NOT NULL DEFAULT '0' COMMENT '错误码',
    `finish_dated`   datetime(6) NOT NULL,
    `start_dated`    datetime(6) NOT NULL,
    `total_task`     int(11) NOT NULL,
    `finish_task`    int(11) NOT NULL,
    `series_id`      varchar(255) DEFAULT NULL,
    PRIMARY KEY (`uuid`),
    UNIQUE KEY `uk_algorithm_task_series_uid` (`series_uid`)
);

-- ----------------------------
-- Table structure for algorithm_task_type
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_task_type`;
CREATE TABLE `algorithm_task_type`
(
    `uuid`        varchar(255) NOT NULL,
    `create_time` datetime(6) NULL DEFAULT NULL,
    `task_type`   varchar(1)   NOT NULL,
    `comment`     varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for algorithm_result
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_result`;
CREATE TABLE `algorithm_result`
(
    `uuid`             varchar(255) NOT NULL,
    `image_series`     varchar(255) NOT NULL,
    `algorithm_type`   varchar(255) NOT NULL,
    `algorithm_result` longtext NULL,
    `mask_url`         longtext NULL,
    `index`            int(11) NOT NULL,
    `create_time`      datetime(6) NULL DEFAULT NULL,
    `task_id`          varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE,
    INDEX              `algorithm_result_task_id_9e9fea2c_fk_algorithm_task_uuid`(`task_id` ASC) USING BTREE,
    CONSTRAINT `algorithm_result_task_id_9e9fea2c_fk_algorithm_task_uuid` FOREIGN KEY (`task_id`) REFERENCES `algorithm_task` (`uuid`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- ----------------------------
-- Table structure for aspect_mask_color_map
-- ----------------------------
DROP TABLE IF EXISTS `aspect_mask_color_map`;
CREATE TABLE `aspect_mask_color_map`
(
    `uuid`        varchar(255) NOT NULL,
    `name`        varchar(255) NOT NULL,
    `color`       varchar(255) NULL DEFAULT NULL,
    `index`       int(11) NOT NULL,
    `update_time` datetime(6) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);


-- ----------------------------
-- Table structure for django_migrations
-- ----------------------------
DROP TABLE IF EXISTS `django_migrations`;
CREATE TABLE `django_migrations`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT,
    `app`     varchar(255) NOT NULL,
    `name`    varchar(255) NOT NULL,
    `applied` datetime(6) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

-- ----------------------------
-- Table structure for notification
-- ----------------------------
DROP TABLE IF EXISTS `notification`;
CREATE TABLE `notification`
(
    `uuid`        varchar(255) NOT NULL,
    `content`     longtext     NOT NULL,
    `create_date` datetime(6) NULL DEFAULT NULL,
    `is_read`     tinyint(1) NOT NULL,
    `owner`       varchar(255) NULL DEFAULT NULL,
    `read_date`   datetime(6) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

COMMIT;

USE `cloud`;

-- ----------------------------
-- Table structure for t_config
-- ----------------------------
DROP TABLE IF EXISTS `t_config`;
CREATE TABLE `t_config`
(
    `id`            varchar(255) NOT NULL COMMENT '主键',
    `code`          varchar(64)  NOT NULL DEFAULT '' COMMENT '配置项编码',
    `value`         varchar(512) NOT NULL DEFAULT '' COMMENT '配置项值',
    `default_value` varchar(512) NOT NULL DEFAULT '' COMMENT '配置项默认值',
    `format`        varchar(8)   NOT NULL DEFAULT 'text' COMMENT '属性值格式（text、bool、int、float、json）',
    `category`      varchar(32)  NOT NULL DEFAULT '' COMMENT '分类（ctp、aspects、cta）',
    `tag`           varchar(32)  NOT NULL DEFAULT '' COMMENT '标签',
    `custom`        tinyint      NOT NULL DEFAULT '0' COMMENT '自定义配置（1:是;0:否）',
    `description`   varchar(64)  NOT NULL DEFAULT '' COMMENT '配置描述',
    `gmt_create`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_code` (`code`)
);

-- ----------------------------
-- Records of t_config
-- ----------------------------
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Spatial-Smooth-Kernal-Size', '32', '32', 'float', 'ctp', 'init', 'Spatial-Smooth-Kernal-Size');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Spatial-Smooth-Sigma', '2.5', '2.5', 'float', 'ctp', 'init', 'Spatial-Smooth-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Smooth', 'True', 'True', 'bool', 'ctp', 'init', 'Time-Smooth');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Smooth-Kernal-Size', '-1', '-1', 'int', 'ctp', 'init', 'Time-Smooth-Kernal-Size');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Smooth-Sigma', '0.5', '0.5', 'float', 'ctp', 'init', 'Time-Smooth-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Gamma-Fit', 'False', 'False', 'bool', 'ctp', 'init', 'Gamma-Fit');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Self-Defined-Interval', 'False', 'False', 'bool', 'ctp', 'init', 'Self-Defined-Interval');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Interval', '1.6', '1.6', 'float', 'ctp', 'init', 'Time-Interval');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Total-Scan-Time', '60', '60', 'float', 'ctp', 'init', 'Total-Scan-Time');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Image-Resample', 'True', 'True', 'bool', 'ctp', 'init', 'Image-Resample');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Generate-Maps-Method', '4', '4', 'int', 'ctp', 'init', 'Generate-Maps-Method');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Feature-Smooth-Type', '-1', '-1', 'int', 'ctp', 'init', 'Feature-Smooth-Type');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-CBF-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-CBF-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-CBV-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-CBV-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-MTT-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-MTT-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-TMax-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-TMax-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-TTP-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-TTP-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-PS-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-PS-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF30-Optimize-Thresholds', '15,0.02,0.2', '15,0.02,0.2', 'text', 'ctp', 'init', 'CBF30-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF34-Optimize-Thresholds', '15,0.02,0.2', '15,0.02,0.2', 'text', 'ctp', 'init', 'CBF34-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF38-Optimize-Thresholds', '15,0.02,0.2', '15,0.02,0.2', 'text', 'ctp', 'init', 'CBF38-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF50-Optimize-Thresholds', '5,0.2,0.5', '5,0.2,0.5', 'text', 'ctp', 'init', 'CBF50-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF70-Optimize-Thresholds', '3,0.2,0.5', '3,0.2,0.5', 'text', 'ctp', 'init', 'CBF70-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax10-Optimize-Thresholds', '10,0.02,0.5', '10,0.02,0.5', 'text', 'ctp', 'init', 'TMax10-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax8-Optimize-Thresholds', '10,0.03,0.5', '10,0.03,0.5', 'text', 'ctp', 'init', 'TMax8-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax6-Optimize-Thresholds', '10,0.05,0.5', '10,0.05,0.5', 'text', 'ctp', 'init', 'TMax6-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax4-Optimize-Thresholds', '3,0.2,0.5', '3,0.2,0.5', 'text', 'ctp', 'init', 'TMax4-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Remove-Csf', 'False', 'False', 'bool', 'ctp', 'init', 'Remove-Csf');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Remove-CsfC', 'False', 'False', 'bool', 'ctp', 'init', 'Remove-CsfC');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Remove-Vessel', 'False', 'False', 'bool', 'ctp', 'init', 'Remove-Vessel');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Generate-Ill-Images-Summary', 'False', 'False', 'bool', 'ctp', 'init', 'Generate-Ill-Images-Summary');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Draw-Ill-Images-Index', 'True', 'True', 'bool', 'ctp', 'init', 'Draw-Ill-Images-Index');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Mip-LW', '80,150', '80,150', 'text', 'ctp', 'init', 'Mip-LW');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'minCTPcount', '200', '200', 'int', 'ctp', '', 'CTP最小图像张数');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'algorithmProcessMode', '1', '1', 'int', 'ctp', '', '算法处理方式');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'colorTable', 'jet', 'jet', 'text', 'platform', '', 'Viewer默认伪彩');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'canReportBack', 'True', 'True', 'bool', 'platform', '', '报告回传开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportAspectsSummary', 'True', 'True', 'bool', 'platform', '', 'ASPECTS结果图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtpSummary', 'True', 'True', 'bool', 'platform', '', 'CTP结果图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtpColorMap', 'True', 'True', 'bool', 'platform', '', 'CTP伪彩图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtpMip', 'True', 'True', 'bool', 'platform', '', 'CTP MIP图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtaVr', 'True', 'True', 'bool', 'platform', '', 'CTA VR图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtaMip', 'True', 'True', 'bool', 'platform', '', 'CTA 三维MIP图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportBackMethod', 'orthanc', 'orthanc', 'text', 'platform', '', '报告回传方式');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Threshold-Segmentation-Range', '20,100', '20,100', 'float', 'ctp', 'init', '阈值分割范围');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctpReportMerge', '2', '2', 'int', 'ctp', '', 'CTP报告合并');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctpFalseColor', 'jet', 'jet', 'text', 'ctp', 'init', 'CTP参数图伪彩');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Motion-Correction', 'True', 'True', 'bool', 'ctp', 'init', 'Motion-Correction');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'threshold_low', '0.3', '0.3', 'float', 'aspects', 'init', 'threshold_low');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'threshold_high', '0.8', '0.8', 'float', 'aspects', 'init', 'threshold_high');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'margin', '0.5', '0.5', 'float', 'aspects', 'init', 'margin');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'n_layer_template', '36.0', '36.0', 'float', 'aspects', 'init', 'n_layer_template');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'up_layer_template', '46.0', '46.0', 'float', 'aspects', 'init', 'up_layer_template');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'roi_thre', '240.0', '240.0', 'float', 'aspects', 'init', 'roi_thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'roi', '220.0', '220.0', 'float', 'aspects', 'init', 'roi');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'batch_size', '4.0', '4.0', 'float', 'aspects', 'init', 'batch_size');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'keep_largest_connected_component', '1.0', '1.0', 'float', 'aspects', 'init', 'keep_largest_connected_component');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'thre', '0.96', '0.96', 'float', 'aspects', 'init', 'thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reverse', '0', '0', 'float', 'aspects', 'init', 'reverse');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), '3d_thre', '0.96', '0.96', 'float', 'aspects_3d', 'init', '3d thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), '3d_reverse', '0', '0', 'float', 'aspects_3d', 'init', '3d reverse');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'GE_CALLBACK_START', '0', '0', 'int', 'platform', '', 'GE开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'aspects_3d_switch', '0', '0', 'int', 'aspects', '', 'ASPECTS 3D 开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctp_pcolor_source', 'nifti', 'nifti', 'text', 'platform', '', 'CTP伪彩显示源');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Spatial-Smooth', 'True', 'True', 'bool', 'ctp', 'init', 'Spatial-Smooth');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Spatial-Smooth-Method', '1', '1', 'int', 'ctp', 'init', 'Spatial-Smooth-Method');


-- ----------------------------
-- Table structure for t_study
-- ----------------------------
DROP TABLE IF EXISTS `t_study`;
CREATE TABLE `t_study`
(
    `id`                 varchar(36)  NOT NULL COMMENT '主键',
    `study_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '检查实例唯一标识符',
    `study_id`           varchar(32)  NOT NULL DEFAULT '' COMMENT '检查编号',
    `study_datetime`     datetime              DEFAULT NULL COMMENT '检查日期时间',
    `study_description`  varchar(255) NOT NULL DEFAULT '' COMMENT '检查描述',
    `patient_id`         varchar(64)  NOT NULL DEFAULT '' COMMENT '患者编号',
    `patient_name`       varchar(64)  NOT NULL DEFAULT '' COMMENT '患者名称',
    `patient_sex`        varchar(32)  NOT NULL DEFAULT '' COMMENT '患者性别',
    `patient_age`        varchar(32)  NOT NULL DEFAULT '' COMMENT '患者年龄',
    `patient_weight`     varchar(32)  NOT NULL DEFAULT '' COMMENT '患者体重',
    `patient_birthdate`  varchar(8)   NOT NULL DEFAULT '' COMMENT '患者出生日期',
    `api_version`        varchar(32)  NOT NULL DEFAULT '' COMMENT '接口版本',
    `orthanc_id`         varchar(255) NOT NULL DEFAULT '' COMMENT 'Orthanc标识',
    `gmt_create`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_study_instance_uid` (`study_instance_uid`),
    KEY                  `idx_study_gmt_modified` (`gmt_modified`)
);
-- ----------------------------
-- Table structure for t_series
-- ----------------------------
DROP TABLE IF EXISTS `t_series`;
CREATE TABLE `t_series`
(
    `id`                  varchar(255) NOT NULL COMMENT '主键',
    `series_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '序列实例唯一标识符',
    `modality`            varchar(64)  NOT NULL DEFAULT '' COMMENT '设备类型',
    `series_description`  varchar(255) NOT NULL DEFAULT '' COMMENT '序列描述',
    `series_number`       varchar(16)  NOT NULL DEFAULT '' COMMENT '序列编号',
    `series_date`         varchar(8)   NOT NULL DEFAULT '' COMMENT '序列拍摄日期',
    `series_time`         varchar(16)  NOT NULL DEFAULT '' COMMENT '序列拍摄时间',
    `body_part_examined`  varchar(32)  NOT NULL DEFAULT '' COMMENT '检查部位',
    `slice_thickness`     varchar(16)  NOT NULL DEFAULT '' COMMENT '层厚',
    `type`                varchar(32)  NOT NULL DEFAULT '' COMMENT '序列类型（aspects/aspects_ar/ctp/ctp_ar/cta/cta_ar/other）',
    `downloaded`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '下载完成（0:否;1:是）',
    `finish_percent`      smallint(6) NOT NULL DEFAULT '0' COMMENT '算法进度',
    `thumbnail`           varchar(255) NOT NULL DEFAULT '' COMMENT '缩略图',
    `thumbnail_path`      varchar(512) NOT NULL DEFAULT '' COMMENT '缩略图地址',
    `original_series`     varchar(128) NOT NULL DEFAULT '' COMMENT '原始序列',
    `geapi_status`        smallint         NULL            COMMENT 'GE api状态',
    `orthanc_id`          varchar(255) NOT NULL DEFAULT '' COMMENT 'Orthanc标识',
    `study_id`            varchar(255) NOT NULL COMMENT '检查ID',
    `gmt_create`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_series_instance_uid` (`series_instance_uid`),
    KEY                   `fk_series_study_id` (`study_id`),
    CONSTRAINT `fk_series_study_id` FOREIGN KEY (`study_id`) REFERENCES `t_study` (`id`)
);

-- ----------------------------
-- Table structure for t_series_algorithm
-- ----------------------------
DROP TABLE IF EXISTS `t_series_algorithm`;
CREATE TABLE `t_series_algorithm`
(
    `id`                  varchar(255)  NOT NULL COMMENT '主键',
    `series_instance_uid` varchar(128)  NOT NULL DEFAULT '' COMMENT '序列实例唯一标识符',
    `comment`             varchar(1024) NOT NULL DEFAULT '' COMMENT '算法匹配说明',
    `gmt_create`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_series_alg_instance_uid` (`series_instance_uid`)
);

-- ----------------------------
-- Table structure for t_series_algorithm
-- ----------------------------
DROP TABLE IF EXISTS `t_feature_map`;
CREATE TABLE `t_feature_map`
(
    `id`                  varchar(255) NOT NULL COMMENT '主键',
    `study_instance_uid`  varchar(128) NOT NULL DEFAULT '' COMMENT '检查实例唯一标识符',
    `series_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '序列实例唯一标识符',
    `type`                varchar(16)  NOT NULL DEFAULT '' COMMENT '类型（cbf, cbv, mtt, tmax, ttp, ps）',
    `window_width`        varchar(32)  NOT NULL DEFAULT '' COMMENT '自适应窗宽',
    `window_level`        varchar(32)  NOT NULL DEFAULT '' COMMENT '自适应窗位',
    `path`                varchar(512) NOT NULL DEFAULT '' COMMENT '保存路径',
    `gmt_create`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_feature_map_study_series` (`study_instance_uid`,`series_instance_uid`)
);

-- ----------------------------
-- Table structure for user_user
-- ----------------------------
DROP TABLE IF EXISTS `user_user`;
CREATE TABLE `user_user`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `password`        varchar(128) NOT NULL,
    `last_login`      datetime(6) NULL DEFAULT NULL,
    `is_superuser`    tinyint(1) NOT NULL,
    `username`        varchar(150) NOT NULL,
    `first_name`      varchar(30)  NOT NULL,
    `last_name`       varchar(150) NOT NULL,
    `email`           varchar(254) NOT NULL,
    `is_staff`        tinyint(1) NOT NULL,
    `is_active`       tinyint(1) NOT NULL,
    `date_joined`     datetime(6) NOT NULL,
    `full_name`       varchar(255) NOT NULL,
    `last_login_time` datetime(6) NOT NULL,
    `wechat_openid`   varchar(255) NULL DEFAULT NULL,
    `phone`           varchar(255) NULL DEFAULT NULL,
    `photo_url`       varchar(255) NULL DEFAULT NULL,
    `access_id`       varchar(255) NULL DEFAULT NULL,
    `role_type`       varchar(255) NULL DEFAULT NULL,
    `is_delete`       tinyint(1) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `username`(`username` ASC) USING BTREE
);

-- ----------------------------
-- Table structure for auth_group
-- ----------------------------
DROP TABLE IF EXISTS `auth_group`;
CREATE TABLE `auth_group`
(
    `id`   int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(80) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `name`(`name` ASC) USING BTREE
);

-- ----------------------------
-- Table structure for user_user_groups
-- ----------------------------
DROP TABLE IF EXISTS `user_user_groups`;
CREATE TABLE `user_user_groups`
(
    `id`       int(11) NOT NULL AUTO_INCREMENT,
    `user_id`  int(11) NOT NULL,
    `group_id` int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `user_user_groups_user_id_group_id_bb60391f_uniq`(`user_id` ASC, `group_id` ASC) USING BTREE,
    INDEX      `user_user_groups_group_id_c57f13c0_fk_auth_group_id`(`group_id` ASC) USING BTREE,
    CONSTRAINT `user_user_groups_group_id_c57f13c0_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `user_user_groups_user_id_13f9a20d_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
);


-- ----------------------------
-- Table structure for django_content_type
-- ----------------------------
DROP TABLE IF EXISTS `django_content_type`;
CREATE TABLE `django_content_type`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `app_label` varchar(100) NOT NULL,
    `model`     varchar(100) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `django_content_type_app_label_model_76bd3d3b_uniq`(`app_label` ASC, `model` ASC) USING BTREE
);

-- ----------------------------
-- Table structure for django_migrations
-- ----------------------------
DROP TABLE IF EXISTS `django_migrations`;
CREATE TABLE `django_migrations`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT,
    `app`     varchar(255) NOT NULL,
    `name`    varchar(255) NOT NULL,
    `applied` datetime(6) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

-- ----------------------------
-- Table structure for django_session
-- ----------------------------
DROP TABLE IF EXISTS `django_session`;
CREATE TABLE `django_session`
(
    `session_key`  varchar(40) NOT NULL,
    `session_data` longtext    NOT NULL,
    `expire_date`  datetime(6) NOT NULL,
    PRIMARY KEY (`session_key`) USING BTREE,
    INDEX          `django_session_expire_date_a5c62663`(`expire_date` ASC) USING BTREE
);

-- ----------------------------
-- Table structure for django_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `django_admin_log`;
CREATE TABLE `django_admin_log`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `action_time`     datetime(6) NOT NULL,
    `object_id`       longtext NULL,
    `object_repr`     varchar(200) NOT NULL,
    `action_flag`     smallint(5) UNSIGNED NOT NULL,
    `change_message`  longtext     NOT NULL,
    `content_type_id` int(11) NULL DEFAULT NULL,
    `user_id`         int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX             `django_admin_log_content_type_id_c4bce8eb_fk_django_co`(`content_type_id` ASC) USING BTREE,
    INDEX             `django_admin_log_user_id_c564eba6_fk_user_user_id`(`user_id` ASC) USING BTREE,
    CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `django_admin_log_user_id_c564eba6_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- ----------------------------
-- Table structure for auth_permission
-- ----------------------------
DROP TABLE IF EXISTS `auth_permission`;
CREATE TABLE `auth_permission`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `name`            varchar(255) NOT NULL,
    `content_type_id` int(11) NOT NULL,
    `codename`        varchar(100) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `auth_permission_content_type_id_codename_01ab375a_uniq`(`content_type_id` ASC, `codename` ASC) USING BTREE,
    CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- ----------------------------
-- Table structure for auth_group_permissions
-- ----------------------------
DROP TABLE IF EXISTS `auth_group_permissions`;
CREATE TABLE `auth_group_permissions`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `group_id`      int(11) NOT NULL,
    `permission_id` int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `auth_group_permissions_group_id_permission_id_0cd325b0_uniq`(`group_id` ASC, `permission_id` ASC) USING BTREE,
    INDEX           `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm`(`permission_id` ASC) USING BTREE,
    CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- ----------------------------
-- Table structure for user_user_user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `user_user_user_permissions`;
CREATE TABLE `user_user_user_permissions`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `user_id`       int(11) NOT NULL,
    `permission_id` int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `user_user_user_permissions_user_id_permission_id_64f4d5b8_uniq`(`user_id` ASC, `permission_id` ASC) USING BTREE,
    INDEX           `user_user_user_permi_permission_id_ce49d4de_fk_auth_perm`(`permission_id` ASC) USING BTREE,
    CONSTRAINT `user_user_user_permi_permission_id_ce49d4de_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `user_user_user_permissions_user_id_31782f58_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- ----------------------------
-- Table structure for authorizationcode
-- ----------------------------
DROP TABLE IF EXISTS `authorizationcode`;
CREATE TABLE `authorizationcode`
(
    `uuid`            varchar(255) NOT NULL,
    `create_user`     varchar(255) NOT NULL,
    `auth_local`      varchar(255) NOT NULL,
    `auth_code`       longtext     NOT NULL,
    `auth_period`     int(11) NOT NULL,
    `auth_start_date` datetime(6) NULL DEFAULT NULL,
    `auth_end_date`   datetime(6) NULL DEFAULT NULL,
    `timestamp`       datetime(6) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for algorithm_type
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_type`;
CREATE TABLE `algorithm_type`
(
    `uuid`             varchar(255) NOT NULL,
    `type`             varchar(255) NOT NULL,
    `keyword`          varchar(255) NULL DEFAULT NULL,
    `value`            varchar(255) NULL DEFAULT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for callback_dicom
-- ----------------------------
DROP TABLE IF EXISTS `callback_dicom`;
CREATE TABLE `callback_dicom`
(
    `uuid`                varchar(255) NOT NULL,
    `sop_instance_uid`    varchar(128) DEFAULT NULL,
    `study_instance_uid`  varchar(128) DEFAULT NULL,
    `series_instance_uid` varchar(128) DEFAULT NULL,
    `instance_number`     smallint(6) NOT NULL DEFAULT '0' COMMENT '影像编号',
    `patient_id`          varchar(255) DEFAULT NULL,
    `patient_name`        varchar(255) DEFAULT NULL,
    `path`                varchar(512) NOT NULL DEFAULT '' COMMENT '图像地址',
    `updatetimestamp`     datetime(6) NOT NULL,
    `timestamp`           datetime(6) NOT NULL,
    `number`              int(11) NOT NULL,
    `is_pushed`           tinyint(1) NOT NULL,
    `download_status`     tinyint(1) NOT NULL,
    `timeout`             varchar(255) DEFAULT NULL,
    `aet`                 varchar(255) DEFAULT NULL,
    `is_first_download`   tinyint(1) NOT NULL,
    `ip`                  varchar(255) DEFAULT NULL,
    `port`                varchar(255) DEFAULT NULL,
    `sop_orthanc_uuid`    varchar(255) DEFAULT NULL,
    `series_orthanc_uuid` varchar(255) DEFAULT NULL,
    `comment`             longtext,
    `study_description`   longtext,
    `modality`            longtext,
    `series_description`  longtext,
    `protocol_name`       longtext,
    `slice_thickness`     longtext,
    PRIMARY KEY (`uuid`) USING BTREE,
    UNIQUE KEY `uk_callback_dicom_sop_uid` (`sop_instance_uid`),
    KEY                   `idx_callback_dicom_001` (`study_instance_uid`,`series_instance_uid`) USING BTREE
);

-- ----------------------------
-- Table structure for doctor_assistant_relation
-- ----------------------------
DROP TABLE IF EXISTS `doctor_assistant_relation`;
CREATE TABLE `doctor_assistant_relation`
(
    `uuid`             varchar(255) NOT NULL,
    `doctor_id`        varchar(255) NULL DEFAULT NULL,
    `employee_id`      varchar(255) NULL DEFAULT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `is_delete`        tinyint(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`uuid`) USING BTREE,
    INDEX              `index_doctor_assistant_relation_doctor_id`(`doctor_id` ASC) USING BTREE,
    INDEX              `index_doctor_assistant_relation_employee_id`(`employee_id` ASC) USING BTREE
);

-- ----------------------------
-- Table structure for mail_auto
-- ----------------------------
DROP TABLE IF EXISTS `mail_auto`;
CREATE TABLE `mail_auto`
(
    `uuid`             varchar(255) NOT NULL,
    `type`             varchar(255) NOT NULL,
    `status`           varchar(255) NULL DEFAULT NULL,
    `auto`             int(11) NOT NULL,
    `method`           int(11) NOT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for mail_config
-- ----------------------------
DROP TABLE IF EXISTS `mail_config`;
CREATE TABLE `mail_config`
(
    `uuid`             varchar(255) NOT NULL,
    `address`          varchar(255) NULL DEFAULT NULL,
    `password`         varchar(255) NULL DEFAULT NULL,
    `type`             varchar(255) NOT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for mail_history
-- ----------------------------
DROP TABLE IF EXISTS `mail_history`;
CREATE TABLE `mail_history`
(
    `uuid`             varchar(255) NOT NULL,
    `send_address`     varchar(255) NULL DEFAULT NULL,
    `receive_address`  longtext NULL,
    `send_time`        datetime(6) NOT NULL,
    `send_status`      int(11) NOT NULL,
    `object_id`        longtext NULL,
    `message`          varchar(255) NULL DEFAULT NULL,
    `theme`            varchar(255) NULL DEFAULT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for pacs_dicom_num
-- ----------------------------
DROP TABLE IF EXISTS `pacs_dicom_num`;
CREATE TABLE `pacs_dicom_num`
(
    `uuid`                varchar(255) NOT NULL,
    `number`              int(11) NOT NULL,
    `aet`                 varchar(255) NULL DEFAULT NULL,
    `ip`                  varchar(255) NULL DEFAULT NULL,
    `port`                varchar(255) NULL DEFAULT NULL,
    `timestamp`           datetime(6) NOT NULL,
    `comment`             longtext NULL,
    `series_instance_uid` longtext NULL,
    `study_instance_uid`  longtext NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for pacs_server
-- ----------------------------
DROP TABLE IF EXISTS `pacs_server`;
CREATE TABLE `pacs_server`
(
    `id`         varchar(255) NOT NULL,
    `aet`        varchar(255) NOT NULL,
    `ip_address` varchar(255) NOT NULL,
    `port`       int(11) NULL DEFAULT NULL,
    `alie_name`  varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

-- ----------------------------
-- Table structure for pacs_server_asyn_condition
-- ----------------------------
DROP TABLE IF EXISTS `pacs_server_asyn_condition`;
CREATE TABLE `pacs_server_asyn_condition`
(
    `id`             varchar(255) NOT NULL,
    `condition`      varchar(255) NOT NULL,
    `priority_index` int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

-- ----------------------------
-- Table structure for process_unusual_data
-- ----------------------------
DROP TABLE IF EXISTS `process_unusual_data`;
CREATE TABLE `process_unusual_data`
(
    `uuid`                varchar(255) NOT NULL,
    `study_instance_uid`  longtext NULL,
    `series_instance_uid` longtext NULL,
    `patient_id`          varchar(255) NULL DEFAULT NULL,
    `patient_name`        varchar(255) NULL DEFAULT NULL,
    `updatetimestamp`     datetime(6) NOT NULL,
    `timestamp`           datetime(6) NOT NULL,
    `number`              int(11) NOT NULL,
    `comment`             longtext NULL,
    `study_description`   longtext NULL,
    `modality`            longtext NULL,
    `series_description`  longtext NULL,
    `protocol_name`       longtext NULL,
    `slice_thickness`     longtext NULL,
    `task_uuid`           varchar(255) NULL DEFAULT NULL,
    `finish_percent`      int(11) NOT NULL,
    `algorithm_type`      varchar(255) NULL DEFAULT NULL,
    `download_status`     tinyint(1) NOT NULL,
    `download_result`     varchar(255) NULL DEFAULT NULL,
    `download_error`      varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for push_config
-- ----------------------------
DROP TABLE IF EXISTS `push_config`;
CREATE TABLE `push_config`
(
    `uuid`           varchar(255) NOT NULL,
    `algorithm_type` varchar(255) NULL DEFAULT NULL,
    `key`            longtext NULL,
    `value`          longtext NULL,
    `timestamp`      datetime(6) NOT NULL,
    `comment`        longtext NULL,
    `is_delete`      tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for record_time
-- ----------------------------
DROP TABLE IF EXISTS `record_time`;
CREATE TABLE `record_time`
(
    `uuid`                varchar(255) NOT NULL,
    `category`            varchar(64) NULL DEFAULT NULL,
    `series_instance_uid` varchar(256) NULL DEFAULT NULL,
    `sop_instance_uid`    varchar(256) NULL DEFAULT NULL,
    `consume_time`        double NULL DEFAULT NULL,
    `comment`             longtext NULL,
    `create_time`         datetime(6) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for study_case_record
-- ----------------------------
DROP TABLE IF EXISTS `study_case_record`;
CREATE TABLE `study_case_record`
(
    `id`         varchar(255) NOT NULL,
    `patient_id` varchar(255) NOT NULL,
    `study_uid`  varchar(255) NOT NULL,
    `async_date` datetime(6) NULL DEFAULT NULL,
    `is_async`   tinyint(1) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

-- ----------------------------
-- Table structure for token
-- ----------------------------
DROP TABLE IF EXISTS `token`;
CREATE TABLE `token`
(
    `uuid`        varchar(255) NOT NULL,
    `token`       varchar(255) NULL DEFAULT NULL,
    `expire_time` int(11) NULL DEFAULT NULL,
    `time_stamp`  datetime(6) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for user_pacs_relation
-- ----------------------------
DROP TABLE IF EXISTS `user_pacs_relation`;
CREATE TABLE `user_pacs_relation`
(
    `id`             varchar(255) NOT NULL,
    `user_id`        varchar(255) NOT NULL,
    `pacs_server_id` varchar(255) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX            `user_pacs_relation_pacs_server_id_d46db9bf_fk_pacs_server_id`(`pacs_server_id` ASC) USING BTREE,
    CONSTRAINT `user_pacs_relation_pacs_server_id_d46db9bf_fk_pacs_server_id` FOREIGN KEY (`pacs_server_id`) REFERENCES `pacs_server` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- ----------------------------
-- Table structure for version
-- ----------------------------
DROP TABLE IF EXISTS `version`;
CREATE TABLE `version`
(
    `uuid`             varchar(255) NOT NULL,
    `server_type`      varchar(255) NOT NULL,
    `version`          varchar(255) NULL DEFAULT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- View structure for study_view
-- ----------------------------
DROP VIEW IF EXISTS `study_view`;
CREATE VIEW `study_view` AS
select `a`.`uuid`                AS `uuid`,
       `a`.`sop_instance_uid`    AS `sop_instance_uid`,
       `a`.`study_instance_uid`  AS `study_instance_uid`,
       `a`.`series_instance_uid` AS `series_instance_uid`,
       `a`.`patient_id`          AS `patient_id`,
       `a`.`patient_name`        AS `patient_name`,
       `a`.`updatetimestamp`     AS `updatetimestamp`,
       `a`.`timestamp`           AS `timestamp`,
       `a`.`number`              AS `number`,
       `a`.`is_pushed`           AS `is_pushed`,
       `a`.`download_status`     AS `download_status`,
       `a`.`timeout`             AS `timeout`,
       `a`.`aet`                 AS `aet`,
       `a`.`is_first_download`   AS `is_first_download`,
       `a`.`ip`                  AS `ip`,
       `a`.`port`                AS `port`,
       `a`.`sop_orthanc_uuid`    AS `sop_orthanc_uuid`,
       `a`.`series_orthanc_uuid` AS `series_orthanc_uuid`,
       `a`.`comment`             AS `comment`,
       `a`.`study_description`   AS `study_description`,
       `a`.`modality`            AS `modality`,
       `a`.`series_description`  AS `series_description`,
       `a`.`protocol_name`       AS `protocol_name`,
       `a`.`slice_thickness`     AS `slice_thickness`,
       `b`.`uuid`                AS `task_uuid`,
       `b`.`finish_percent`      AS `finish_percent`,
       `b`.`algorithm_type`      AS `algorithm_type`
from ((select `callback_dicom`.`uuid`                AS `uuid`,
              `callback_dicom`.`sop_instance_uid`    AS `sop_instance_uid`,
              `callback_dicom`.`study_instance_uid`  AS `study_instance_uid`,
              `callback_dicom`.`series_instance_uid` AS `series_instance_uid`,
              `callback_dicom`.`patient_id`          AS `patient_id`,
              `callback_dicom`.`patient_name`        AS `patient_name`,
              `callback_dicom`.`updatetimestamp`     AS `updatetimestamp`,
              `callback_dicom`.`timestamp`           AS `timestamp`,
              `callback_dicom`.`number`              AS `number`,
              `callback_dicom`.`is_pushed`           AS `is_pushed`,
              `callback_dicom`.`download_status`     AS `download_status`,
              `callback_dicom`.`timeout`             AS `timeout`,
              `callback_dicom`.`aet`                 AS `aet`,
              `callback_dicom`.`is_first_download`   AS `is_first_download`,
              `callback_dicom`.`ip`                  AS `ip`,
              `callback_dicom`.`port`                AS `port`,
              `callback_dicom`.`sop_orthanc_uuid`    AS `sop_orthanc_uuid`,
              `callback_dicom`.`series_orthanc_uuid` AS `series_orthanc_uuid`,
              `callback_dicom`.`comment`             AS `comment`,
              `callback_dicom`.`study_description`   AS `study_description`,
              `callback_dicom`.`modality`            AS `modality`,
              `callback_dicom`.`series_description`  AS `series_description`,
              `callback_dicom`.`protocol_name`       AS `protocol_name`,
              `callback_dicom`.`slice_thickness`     AS `slice_thickness`
       from `callback_dicom`
       group by `callback_dicom`.`series_instance_uid`) `a` left join `algorithm`.`algorithm_task` `b`
      on ((`a`.`series_instance_uid` = `b`.`series_uid`)));

-- ----------------------------
-- Table structure for t_pdf_report
-- ----------------------------
DROP TABLE IF EXISTS `t_pdf_report`;
CREATE TABLE `t_pdf_report`
(
    `id`                 varchar(255) NOT NULL,
    `patient_name`       varchar(255) DEFAULT NULL,
    `male`               varchar(255) DEFAULT NULL,
    `age`                varchar(255) NOT NULL,
    `check_no`           varchar(255) DEFAULT NULL,
    `image_no`           varchar(255) NOT NULL,
    `check_datetime`     varchar(64)  DEFAULT NULL,
    `create_datetime`    datetime(6) NOT NULL,
    `update_time`        datetime(6) NOT NULL,
    `cta_report_text`    longtext,
    `ctp_report_text`    longtext,
    `ncct_report_text`   longtext,
    `study_instance_uid` varchar(255) DEFAULT NULL,
    `report_date`        varchar(32)  DEFAULT NULL,
    `conclusion`         varchar(255) DEFAULT NULL,
    `audit_doctor`       varchar(32)  DEFAULT NULL,
    `report_doctor`      varchar(32)  DEFAULT NULL,
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `t_delete_study`;
CREATE TABLE `t_delete_study`
(
    `id`            INTEGER AUTO_INCREMENT NOT NULL PRIMARY KEY COMMENT '主键id',
    `count`         INTEGER UNSIGNED NOT NULL COMMENT '数量',
    `deleted_count` INTEGER UNSIGNED NOT NULL COMMENT '已删除数量',
    `start_date`    datetime ( 6 ) NULL COMMENT '开始时间',
    `end_date`      datetime ( 6 ) NULL COMMENT '结束时间',
    `study_list`    LONGTEXT NOT NULL COMMENT '删除的检查',
    `is_finished`   bool     NOT NULL COMMENT '是否完成',
    `gmt_create`    datetime ( 6 ) NOT NULL COMMENT '创建时间',
    `gmt_modified`  datetime ( 6 ) NOT NULL COMMENT '更新时间'
);

DROP TABLE IF EXISTS `t_delete_study_detail`;
CREATE TABLE `t_delete_study_detail`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `study_instance_uid` varchar(255) NOT NULL COMMENT '检查表id',
    `mysql`              tinyint(1) DEFAULT NULL COMMENT 'mysql是否删除',
    `mongo`              tinyint(1) DEFAULT NULL COMMENT 'mongo是否删除',
    `static`             tinyint(1) DEFAULT NULL COMMENT 'static是否删除',
    `orthanc`            tinyint(1) DEFAULT NULL COMMENT 'orthanc是否删除',
    `gmt_create`         datetime(6) NOT NULL COMMENT '创建时间',
    `gmt_modified`       datetime(6) NOT NULL COMMENT '更新时间',
    `delete_study_id`    int(11) NOT NULL COMMENT '检删除查表id',
    `data_info`          longtext COMMENT '数据信息',
    PRIMARY KEY (`id`),
    KEY                  `idx_t_delete_study_detai_delete_study_id_fk` (`delete_study_id`),
    CONSTRAINT `idx_t_delete_study_detai_delete_study_id_fk` FOREIGN KEY (`delete_study_id`) REFERENCES `t_delete_study` (`id`)
);

COMMIT;


-- ----------------------------
-- Records of algorithm_type
-- ----------------------------
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTA', NULL, 'USC-UGuard CTA W/vessel extraction', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTA', NULL, 'USC-UGuard CTA Volume Rendering', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTA', NULL, 'USC-UGuard CTA W/bone Removed', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTA', NULL, 'USC-UGuard CTA w/Vessel Mask', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTA', NULL, 'USC-UGuard CTA MIP', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard CTP/CTA Volume Rendering', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard CTP Summary', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard AIF-VOF Location', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard Perfusion Parameter Maps Colored PS', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard Perfusion Parameter Maps Colored TTP', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard Perfusion Parameter Maps Colored CBV', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard Perfusion Parameter Maps Colored Tmax', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard Perfusion Parameter Maps Colored MTT', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'CTP', NULL, 'USC-UGuard Perfusion Parameter Maps Colored CBF', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'NCCT', NULL, 'USC-UGuard ASPECTS Summary', NOW(), NOW(), NULL, 0);
INSERT INTO `cloud`.`algorithm_type` (`uuid`, `type`, `keyword`, `value`, `timestamp`, `update_timestamp`, `comment`,
                                      `is_delete`)
VALUES (UUID(), 'NCCT', NULL, 'USC-UGuard ICH Summary', NOW(), NOW(), NULL, 0);

-- ----------------------------
-- Records of django_content_type
-- ----------------------------
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (1, 'admin', 'logentry');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (2, 'auth', 'permission');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (3, 'auth', 'group');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (4, 'contenttypes', 'contenttype');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (5, 'sessions', 'session');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (6, 'user', 'user');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (7, 'user', 'authorizationcode');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (8, 'user', 'token');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (9, 'algorithm', 'algorithm_result');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (10, 'algorithm', 'algorithmtask');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (11, 'algorithm', 'algorithmtasktype');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (12, 'algorithm', 'aspectmaskcolormap');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (13, 'algorithm', 'notification');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (14, 'async', 'studyviewmodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (15, 'async', 'callbackdicom');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (16, 'async', 'configset');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (17, 'async', 'pacsdicomnumber');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (18, 'async', 'pacsserver');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (19, 'async', 'pacsserverasyncondition');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (20, 'async', 'recordtime');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (21, 'async', 'studycaserecord');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (22, 'async', 'userpacsrelation');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (23, 'django_celery_beat', 'crontabschedule');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (24, 'django_celery_beat', 'intervalschedule');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (25, 'django_celery_beat', 'periodictask');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (26, 'django_celery_beat', 'periodictasks');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (27, 'django_celery_beat', 'solarschedule');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (28, 'algorithm', 'algorithmcallback');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (29, 'mail', 'algorithmtypemodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (30, 'mail', 'mailautomodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (31, 'mail', 'mailconfigmodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (32, 'mail', 'mailhistorymodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (33, 'mail', 'processunusualdatamodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (34, 'maa', 'algorithmtypemodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (35, 'maa', 'mailautomodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (36, 'maa', 'mailconfigmodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (37, 'maa', 'mailhistorymodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (38, 'maa', 'processunusualdatamodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (39, 'mails', 'algorithmtypemodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (40, 'mails', 'mailautomodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (41, 'mails', 'mailconfigmodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (42, 'mails', 'mailhistorymodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (43, 'mails', 'processunusualdatamodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (44, 'mails', 'versionmodel');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (45, 'report', 'pdfreport');
INSERT INTO `cloud`.`django_content_type` (`id`, `app_label`, `model`)
VALUES (46, 'report', 'userpacssslation');

-- ----------------------------
-- Records of auth_permission
-- ----------------------------
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (1, 'Can add log entry', 1, 'add_logentry');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (2, 'Can change log entry', 1, 'change_logentry');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (3, 'Can delete log entry', 1, 'delete_logentry');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (4, 'Can add permission', 2, 'add_permission');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (5, 'Can change permission', 2, 'change_permission');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (6, 'Can delete permission', 2, 'delete_permission');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (7, 'Can add group', 3, 'add_group');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (8, 'Can change group', 3, 'change_group');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (9, 'Can delete group', 3, 'delete_group');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (10, 'Can add content type', 4, 'add_contenttype');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (11, 'Can change content type', 4, 'change_contenttype');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (12, 'Can delete content type', 4, 'delete_contenttype');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (13, 'Can add session', 5, 'add_session');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (14, 'Can change session', 5, 'change_session');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (15, 'Can delete session', 5, 'delete_session');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (16, 'Can add user', 6, 'add_user');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (17, 'Can change user', 6, 'change_user');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (18, 'Can delete user', 6, 'delete_user');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (19, 'Can add 授权码', 7, 'add_authorizationcode');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (20, 'Can change 授权码', 7, 'change_authorizationcode');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (21, 'Can delete 授权码', 7, 'delete_authorizationcode');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (22, 'Can add token验证', 8, 'add_token');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (23, 'Can change token验证', 8, 'change_token');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (24, 'Can delete token验证', 8, 'delete_token');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (25, 'Can add 算法结果表', 9, 'add_algorithm_result');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (26, 'Can change 算法结果表', 9, 'change_algorithm_result');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (27, 'Can delete 算法结果表', 9, 'delete_algorithm_result');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (28, 'Can add 算法任务', 10, 'add_algorithmtask');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (29, 'Can change 算法任务', 10, 'change_algorithmtask');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (30, 'Can delete 算法任务', 10, 'delete_algorithmtask');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (31, 'Can add 算法运行类型记录', 11, 'add_algorithmtasktype');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (32, 'Can change 算法运行类型记录', 11, 'change_algorithmtasktype');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (33, 'Can delete 算法运行类型记录', 11, 'delete_algorithmtasktype');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (34, 'Can add aspect颜色地图', 12, 'add_aspectmaskcolormap');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (35, 'Can change aspect颜色地图', 12, 'change_aspectmaskcolormap');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (36, 'Can delete aspect颜色地图', 12, 'delete_aspectmaskcolormap');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (37, 'Can add 消息通知', 13, 'add_notification');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (38, 'Can change 消息通知', 13, 'change_notification');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (39, 'Can delete 消息通知', 13, 'delete_notification');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (40, 'Can add study级别任务进度视图', 14, 'add_studyviewmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (41, 'Can change study级别任务进度视图', 14, 'change_studyviewmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (42, 'Can delete study级别任务进度视图', 14, 'delete_studyviewmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (43, 'Can add 回调接收内容', 15, 'add_callbackdicom');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (44, 'Can change 回调接收内容', 15, 'change_callbackdicom');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (45, 'Can delete 回调接收内容', 15, 'delete_callbackdicom');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (46, 'Can add 推送配置表', 16, 'add_configset');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (47, 'Can change 推送配置表', 16, 'change_configset');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (48, 'Can delete 推送配置表', 16, 'delete_configset');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (49, 'Can add pacs厂家series数量', 17, 'add_pacsdicomnumber');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (50, 'Can change pacs厂家series数量', 17, 'change_pacsdicomnumber');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (51, 'Can delete pacs厂家series数量', 17, 'delete_pacsdicomnumber');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (52, 'Can add pacs服务器', 18, 'add_pacsserver');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (53, 'Can change pacs服务器', 18, 'change_pacsserver');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (54, 'Can delete pacs服务器', 18, 'delete_pacsserver');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (55, 'Can add pacs服务器过滤条件', 19, 'add_pacsserverasyncondition');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (56, 'Can change pacs服务器过滤条件', 19, 'change_pacsserverasyncondition');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (57, 'Can delete pacs服务器过滤条件', 19, 'delete_pacsserverasyncondition');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (58, 'Can add 耗时记录表', 20, 'add_recordtime');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (59, 'Can change 耗时记录表', 20, 'change_recordtime');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (60, 'Can delete 耗时记录表', 20, 'delete_recordtime');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (61, 'Can add 影像资料', 21, 'add_studycaserecord');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (62, 'Can change 影像资料', 21, 'change_studycaserecord');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (63, 'Can delete 影像资料', 21, 'delete_studycaserecord');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (64, 'Can add 用户与pacs服务器关系', 22, 'add_userpacsrelation');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (65, 'Can change 用户与pacs服务器关系', 22, 'change_userpacsrelation');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (66, 'Can delete 用户与pacs服务器关系', 22, 'delete_userpacsrelation');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (67, 'Can add crontab', 23, 'add_crontabschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (68, 'Can change crontab', 23, 'change_crontabschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (69, 'Can delete crontab', 23, 'delete_crontabschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (70, 'Can add interval', 24, 'add_intervalschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (71, 'Can change interval', 24, 'change_intervalschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (72, 'Can delete interval', 24, 'delete_intervalschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (73, 'Can add periodic task', 25, 'add_periodictask');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (74, 'Can change periodic task', 25, 'change_periodictask');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (75, 'Can delete periodic task', 25, 'delete_periodictask');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (76, 'Can add periodic tasks', 26, 'add_periodictasks');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (77, 'Can change periodic tasks', 26, 'change_periodictasks');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (78, 'Can delete periodic tasks', 26, 'delete_periodictasks');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (79, 'Can add solar event', 27, 'add_solarschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (80, 'Can change solar event', 27, 'change_solarschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (81, 'Can delete solar event', 27, 'delete_solarschedule');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (82, 'Can add 报告回传', 28, 'add_algorithmcallback');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (83, 'Can change 报告回传', 28, 'change_algorithmcallback');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (84, 'Can delete 报告回传', 28, 'delete_algorithmcallback');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (85, 'Can add 算法分类表', 29, 'add_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (86, 'Can change 算法分类表', 29, 'change_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (87, 'Can delete 算法分类表', 29, 'delete_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (88, 'Can add 邮件自动发送表', 30, 'add_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (89, 'Can change 邮件自动发送表', 30, 'change_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (90, 'Can delete 邮件自动发送表', 30, 'delete_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (91, 'Can add 邮箱配置表', 31, 'add_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (92, 'Can change 邮箱配置表', 31, 'change_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (93, 'Can delete 邮箱配置表', 31, 'delete_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (94, 'Can add 邮件发送历史表', 32, 'add_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (95, 'Can change 邮件发送历史表', 32, 'change_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (96, 'Can delete 邮件发送历史表', 32, 'delete_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (97, 'Can add 处理异常数据', 33, 'add_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (98, 'Can change 处理异常数据', 33, 'change_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (99, 'Can delete 处理异常数据', 33, 'delete_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (100, 'Can add 算法分类表', 34, 'add_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (101, 'Can change 算法分类表', 34, 'change_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (102, 'Can delete 算法分类表', 34, 'delete_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (103, 'Can add 邮件自动发送表', 35, 'add_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (104, 'Can change 邮件自动发送表', 35, 'change_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (105, 'Can delete 邮件自动发送表', 35, 'delete_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (106, 'Can add 邮箱配置表', 36, 'add_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (107, 'Can change 邮箱配置表', 36, 'change_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (108, 'Can delete 邮箱配置表', 36, 'delete_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (109, 'Can add 邮件发送历史表', 37, 'add_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (110, 'Can change 邮件发送历史表', 37, 'change_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (111, 'Can delete 邮件发送历史表', 37, 'delete_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (112, 'Can add 处理异常数据', 38, 'add_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (113, 'Can change 处理异常数据', 38, 'change_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (114, 'Can delete 处理异常数据', 38, 'delete_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (115, 'Can add 算法分类表', 39, 'add_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (116, 'Can change 算法分类表', 39, 'change_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (117, 'Can delete 算法分类表', 39, 'delete_algorithmtypemodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (118, 'Can add 邮件自动发送表', 40, 'add_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (119, 'Can change 邮件自动发送表', 40, 'change_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (120, 'Can delete 邮件自动发送表', 40, 'delete_mailautomodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (121, 'Can add 邮箱配置表', 41, 'add_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (122, 'Can change 邮箱配置表', 41, 'change_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (123, 'Can delete 邮箱配置表', 41, 'delete_mailconfigmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (124, 'Can add 邮件发送历史表', 42, 'add_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (125, 'Can change 邮件发送历史表', 42, 'change_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (126, 'Can delete 邮件发送历史表', 42, 'delete_mailhistorymodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (127, 'Can add 处理异常数据', 43, 'add_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (128, 'Can change 处理异常数据', 43, 'change_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (129, 'Can delete 处理异常数据', 43, 'delete_processunusualdatamodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (130, 'Can add 版本信息', 44, 'add_versionmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (131, 'Can change 版本信息', 44, 'change_versionmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (132, 'Can delete 版本信息', 44, 'delete_versionmodel');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (134, 'Can change PDF报告信息', 45, 'change_pdfreport');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (135, 'Can delete PDF报告信息', 45, 'delete_pdfreport');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (136, 'Can add 用户与pacs服务器关系', 46, 'add_userpacssslation');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (137, 'Can change 用户与pacs服务器关系', 46, 'change_userpacssslation');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (138, 'Can delete 用户与pacs服务器关系', 46, 'delete_userpacssslation');
INSERT INTO `cloud`.`auth_permission` (`id`, `name`, `content_type_id`, `codename`)
VALUES (139, 'Can add PDF 报告信息', 45, 'add_pdfreport');

COMMIT;