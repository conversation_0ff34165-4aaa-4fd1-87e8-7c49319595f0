from django.db import models
from django.contrib.auth.models import AbstractUser
# from server.access.models import access

MAX_LENGTH = 255


# Create your models here.

# 继承Djano自带的User表
class User(AbstractUser):
    full_name = models.CharField(verbose_name='中文全名',
                                 max_length=MAX_LENGTH,
                                 null=False,
                                 blank=False)
    last_login_time = models.DateTimeField(verbose_name='上次登录时间',
                                           auto_now=True)
    wechat_openid = models.CharField(verbose_name='微信openid',
                                     max_length=MAX_LENGTH,
                                     null=True, blank=True)
    phone = models.CharField(verbose_name='电话',
                             max_length=MAX_LENGTH,
                             null=True, blank=True)
    photo_url = models.CharField(verbose_name='角色照片',
                                 max_length=MAX_LENGTH,
                                 null=True, blank=True)
    access_id = models.CharField(verbose_name='权限',
                                 max_length=MAX_LENGTH,
                                 null=True, blank=True)
    role_type_choise = (
        ('Patient', '病人'),
        ('Doctor', '医生'),
        ('company', '公司')
    )
    role_type = models.CharField(verbose_name='角色类型', choices=role_type_choise,
                                 max_length=MAX_LENGTH, blank=True, null=True)
    is_delete = models.BooleanField(default=False, verbose_name='是否删除账户',
                                    blank=True)

    class Meta(AbstractUser.Meta):
        swappable = 'AUTH_USER_MODEL'


class AuthorizationCode(models.Model):
    uuid = models.CharField(primary_key=True,
                            max_length=MAX_LENGTH,
                            verbose_name='主键')
    create_user = models.CharField(verbose_name='创建者',
                                   max_length=MAX_LENGTH,
                                   null=False,
                                   blank=False)
    auth_local = models.CharField(verbose_name="被授权",
                                  null=False,
                                  max_length=MAX_LENGTH,
                                  blank=False
                                  )
    auth_code = models.TextField(verbose_name="授权码",
                                 null=False,
                                 blank=False
                                 )
    auth_period = models.IntegerField(default=0,
                                      verbose_name="授权周期天数")
    auth_start_date = models.DateTimeField(null=True,
                                           blank=True,
                                           verbose_name="授权开始时间")
    auth_end_date = models.DateTimeField(null=True,
                                         blank=True,
                                         verbose_name="授权结束时间")
    timestamp = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")

    class Meta:
        db_table = 'authorizationcode'
        verbose_name = '授权码'
        verbose_name_plural = '授权码'

    def to_dict(self):
        create_user_name = ""
        if self.create_user:
            user_queryset = User.objects.filter(id=int(self.create_user))
            if user_queryset:
                user_obj = user_queryset.first()
                create_user_name = user_obj.username
        return dict(
            uuid=self.uuid,
            create_user=self.create_user,
            create_user_name=create_user_name,
            auth_start_date=self.auth_start_date.strftime("%Y-%m-%d"),
            auth_period=self.auth_period,
            auth_end_date=self.auth_end_date.strftime("%Y-%m-%d"),
            auth_code=self.auth_code,
            auth_local=self.auth_local,
            timestamp=self.timestamp

        )


class AdminUser(models.Model):
    account = models.CharField(verbose_name='用户', max_length=MAX_LENGTH)
    password = models.CharField(verbose_name="密码", max_length=MAX_LENGTH)
    is_delete = models.BooleanField(default=False, verbose_name="是否删除")

    class Meta:
        db_table = 't_user'
        verbose_name = '登录信息'
        verbose_name_plural = '登录信息'


class AuthorizationRecord(models.Model):
    auth_code = models.TextField(verbose_name="授权码", null=False, blank=False)
    gmt_create = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'authorization_record'
        verbose_name = '授权码记录'
        verbose_name_plural = '授权码记录'

