from datetime import datetime


def gen_password(phone):
    """
    生成password
    """
    if not phone:
        phone = datetime.now().year
    return '{}@123'.format(phone)


def validate_data(model, reuqest_body=None, is_create=False, validated_field=list()):
    save_data = dict()
    # mysql_field = dict()
    # validated_field = ['phone', 'treatment_selection', 'treatment_history']
    if is_create:
        result = set(validated_field) - reuqest_body.keys()
        if result:
            return False, '{},请传入该字段'.format(str(result))
        else:
            for field in validated_field:
                is_field = reuqest_body.pop(field, None)
                if is_field != '':
                    save_data[field] = is_field
                else:
                    return False, '{}不能为空'.format(field)
    mysql_field = {i.name: True for i in model._meta.fields}
    # for i in model._meta.fields:
    #     mysql_field[i.name] = True
    for key, value in reuqest_body.items():
        if mysql_field.get(key, None):
            save_data[key] = value
    return True, save_data

# def validate_data(model, is_create=False, reuqest_body=None):
#     save_data = dict()
#     mysql_field = dict()
#     for i in model._meta.fields:
#         mysql_field[i.name] = True
#     for key, value in reuqest_body.items():
#         if mysql_field[key] and value != '':
#             if str(ArticleType) == str(model):
#                 if key == "catgory":
#                     try:
#                         save_data[key] = Catgory.objects.get(uuid=value)
#                         print(save_data)
#                     except Exception:
#                         print(traceback.format_exc())
#                         return False, '该菜单栏uuid不存在'
#                 else:
#                     save_data[key] = value
#             elif str(model) == str(ArticleTitle):
#                 if key == "article_type":
#                     try:
#                         save_data[key] = ArticleType.objects.get(uuid=value)
#                     except Exception:
#                         print(traceback.format_exc())
#                         return False, '该文章类型uuid不存在'
#                 else:
#                     save_data[key] = value
#             elif str(model) == str(ArticleContent):
#                 if key == "article_title":
#                     try:
#                         save_data[key] = ArticleTitle.objects.get(uuid=value)
#                     except Exception:
#                         print(traceback.format_exc())
#                         return False, '该文章标题uuid不存在'
#                 else:
#                     save_data[key] = value
#             else:
#                 save_data[key] = value
#         elif not mysql_field[key]:
#             return False, '{}该字段没有'.format(key)
#
#         elif value == '' and str(model) != str(ArticleContent):
#             return False, '{}该字段不能为空'.format(key)
#     if is_create:
#         save_data['uuid'] = str(uuid.uuid1())
#         save_data['timestamp'] = (datetime.datetime.now()).strftime("%Y-%m-%d %H:%M:%S")
#     else:
#         save_data['timestamp'] = (datetime.datetime.now()).strftime("%Y-%m-%d %H:%M:%S")
#     return True, save_data
