import uuid
import re

from django.http import JsonResponse
from django.contrib.auth import authenticate, login
from server.common.views import BaseView
from server.common.base import check_auth_code
from .utils import gen_password, validate_data
from .models import User, AuthorizationCode, AdminUser, AuthorizationRecord
import traceback
from server.user.auth import AuthVerify
import datetime
from server.common.code import RetCode

# 验证码
# class Code(BaseView):
#     # 验证码获取
#     def get(self, request):
#         response = self.response
#         ip = get_ip(request)
#         if not ip:
#             response["message"] = "获取验证码失败"
#             return JsonResponse(response)
#         print(ip)
#         try:
#             if request.session.get(ip, None):
#                 os.remove(request.session[ip]["file_root"])
#                 del request.session[ip]
#             name = str(uuid.uuid1()) + ".png"
#             file_root = os.path.join(UPLOAD_PICTURE_PATH, name)
#             img = ValidCodeImg(img_format=file_root)
#             valid_str = img.getValidCodeImg()
#             request.session[str(ip)] = dict(code=valid_str, file_root=file_root)
#             # 设置过期时间
#             request.session.set_expiry(60)
#             url = STATIC_URL + "upload_picture/" + name
#             response["data"] = dict(url=url)
#             response["status"] = True
#             response["code"] = 200
#             return JsonResponse(response)
#         except Exception:
#             print(traceback.format_exc())
#             response["message"] = "获取验证码失败"
#             return JsonResponse(response)
#
#
# # 创建登录账户（APIView)
# # get/post/update/delete
# # 需要通过role_type与3个不同角色身份相关联
# 用户登陆



class Login(BaseView):
    def post(self, request):
        username = request.POST.get('username', None)
        password = request.POST.get('password', None)
        # 校验数据
        if not all([username, password]):
            return self.fail(400, '用户名或密码为空')
        # 登录校验：检查用户名是否存在
        find_count = User.objects.filter(username=username, is_delete=False)
        if not find_count:
            return self.fail(400, '用户不存在')
        # 登录校验：验证用户密码
        user = authenticate(username=username, password=password)
        if user:
            # 记住用户登陆状态
            login(request, user)
            """
            todo 登录后的接口认证
            """
            # token_generator = TokenGenerator()
            body = {'id': user.id, 'fullname': user.full_name, 'username': username}
            return self.ok(body)
        else:
            return self.fail(400, '密码不正确')



class Register(BaseView):
    def post(self, request):
        response = self.response
        data = request.POST
        status, validated_data = validate_data(User, reuqest_body=data, is_create=True,
                                               validated_field=["username", "phone",'email'])
        if not status:
            response["message"] = validated_data
            return JsonResponse(response)
        phone = validated_data.get('phone')
        verify_phone = re.match(r'^1\d{10}$', phone)
        if not verify_phone:
            response["message"] = '手机号格式不正确'
            return JsonResponse(response)
        email = validated_data.get('email')
        verify_email = re.match(
            r'^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){0,4}@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){0,4}$', email)
        if not verify_email:
            response["message"] = '邮箱格式不正确'
            return JsonResponse(response)
        full_name = validated_data.get("username")
        validated_data['username'] = phone
        validated_data['full_name'] = full_name

        find_count = User.objects.filter(full_name=full_name).count()
        if find_count > 0:
            response["message"] = "用户已占用请换一个"
            return JsonResponse(response)
        try:
            phone = validated_data.get('phone')
            validated_data['password'] = gen_password(phone)
            _user = User.objects.create_user(**validated_data)
        except Exception:
            print(traceback.format_exc())
            response["message"] = "创建失败"
            return JsonResponse(response)
        response["message"] = "创建成功"
        response["status"] = True
        response["code"] = 200
        return JsonResponse(response)


# class UserView(APIview):
#     def put(self, request, pk):
#         response = self.response
#         data = request.PUT
#         if not pk:
#             response["message"] = "路由中没有uuid"
#             return JsonResponse(response)
#         pk_check = User.objects.filter(id=pk, is_delete=False)
#         if not pk_check:
#             response["message"] = "用户uuid不存在"
#             return JsonResponse(response)
#         status, validated_data = validate_data(User, reuqest_body=data)
#         if not status:
#             response["message"] = validated_data
#             return JsonResponse(response)
#         password = validated_data.pop("password", None)
#         if password:
#             validated_data["password"] = make_password(password)
#         username = validated_data.get("username", None)
#         if username != pk_check.first().username:
#             duplicate_username = User.objects.filter(username=username)
#             if duplicate_username:
#                 response["message"] = "用户名username已占用请换一个"
#                 return JsonResponse(response)
#         try:
#             pk_check.update(**validated_data)
#         except Exception:
#             print(traceback.format_exc())
#             response['message'] = "修改失败"
#             return JsonResponse(response)
#         response['message'] = '修改成功'
#         response['status'] = True
#         response['code'] = 200
#         return JsonResponse(response)
#
#     def delete(self, request, pk):
#         response = self.response
#         if not pk:
#             response["message"] = "路由中没有uuid"
#             return JsonResponse(response)
#         pk_check = User.objects.filter(id=pk, is_delete=False)
#         if not pk_check:
#             response["message"] = "用户id不存在"
#             return JsonResponse(response)
#         pk_check.update(is_delete=True)
#         response['message'] = '删除成功'
#         response['status'] = True
#         response['code'] = 200
#         return JsonResponse(response)


class AuthorizationCodeLogin(BaseView):
    def post(self, request):
        response = self.response
        data = request.POST
        auth_code = data.get('auth_code', None)
        if not auth_code:
            response['message'] = "请输入授权码"
            return JsonResponse(response)
        try:
            auth_record_qs = AuthorizationRecord.objects.filter(auth_code=auth_code)
            if auth_record_qs.exists():
                response["message"] = "此授权码已经被使用过"
                return JsonResponse(response)
            _auth = AuthVerify()
            start_time, period, modules_str = _auth.decrypt(auth_code)
            if not start_time or period is None:
                response["message"] = "授权失败"
                return JsonResponse(response)
            queryset = AuthorizationCode.objects.all()
            if queryset:
                # 有授权码
                queryset.delete()

            now_time = datetime.datetime.now()
            end_time = start_time + datetime.timedelta(days=period)
            if not (now_time > start_time and now_time < end_time):
                response["message"] = "授权码已过期，请重新获取授权码"
                return JsonResponse(response)
            AuthorizationCode.objects.create(
                uuid=str(uuid.uuid1()),
                create_user="admin",
                auth_local=modules_str,
                auth_end_date=end_time,
                auth_code=auth_code,
                auth_period=period,
                auth_start_date=start_time
            )
            AuthorizationRecord.objects.create(
                auth_code=auth_code
            )
        except Exception:
            print(traceback.format_exc())
            response["message"] = "请确认输入的激活码是否正确"
            return JsonResponse(response)
        response["message"] = "登录成功"
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class CheckoutAuthorizationCodeView(BaseView):
    def get(self, request):
        response = self.response
        creator, end_date, auth_module_list = AuthVerify().get_auth_code_and_module()
        if not creator:
            response["code"] = 413
            response["message"] = "授权异常"
            response["status"] = False
            return JsonResponse(response)
        response["message"] = "登录成功"
        response["code"] = 200
        response["status"] = True
        response["modules"] = auth_module_list
        return JsonResponse(response)


class AdminLogin(BaseView):
    def post(self, request):
        """
        admin页面登录按钮
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        account = request_body.get("account")
        password = request_body.get("password")

        queryset = AdminUser.objects.filter(account=account, password=password, is_delete=False)
        if queryset.exists():
            return self.of(RetCode.OK)

        return self.of(RetCode.UNAUTHORIZED)

