# Generated by Django 2.0.5 on 2021-02-19 09:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuthorizationCode',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='主键')),
                ('create_user', models.CharField(max_length=255, verbose_name='创建者')),
                ('auth_local', models.CharField(max_length=255, verbose_name='被授权')),
                ('auth_code', models.TextField(verbose_name='授权码')),
                ('auth_period', models.IntegerField(default=0, verbose_name='授权周期天数')),
                ('auth_start_date', models.DateTimeField(blank=True, null=True, verbose_name='授权开始时间')),
                ('auth_end_date', models.DateTimeField(blank=True, null=True, verbose_name='授权结束时间')),
                ('timestamp', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
            ],
            options={
                'verbose_name': '授权码',
                'verbose_name_plural': '授权码',
                'db_table': 'authorizationcode',
            },
        ),
    ]
