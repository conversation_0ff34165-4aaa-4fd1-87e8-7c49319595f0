#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : views
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/18 10:10
"""
import logging

from server.image.models import Call<PERSON>ackDICOM
# Create your views here.
from server.common.base import check_auth_code
from server.common.code import RetCode, Const
from server.common.views import BaseView
from server.image.service import ImageCallbackService

log = logging.getLogger("django")


class ImageCallbackView(BaseView):
    """图像回调重构"""

    def post(self, request):
        """
        图像回调

        :param request: 请求
        :return: 响应
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", Const.DEFAULT_HOSPITAL_UID)
        hospital_name = request_body.get("hospitalName", "")
        image_orthanc_id = request_body.get("instanceId", None)
        tags = request_body.get("tags", None)
        if not image_orthanc_id:
            return self.of(RetCode.IMAGE_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.IMAGE_TAGS_IS_EMPTY)
        study_instance_uid = tags.get("StudyInstanceUID", None)
        if not study_instance_uid:
            return self.of(RetCode.STUDY_INSTANCE_UID_IS_EMPTY)
        series_instance_uid = tags.get("SeriesInstanceUID", None)
        if not series_instance_uid:
            return self.of(RetCode.SERIES_INSTANCE_UID_IS_EMPTY)
        sop_instance_uid = tags.get("SOPInstanceUID", None)
        if not sop_instance_uid:
            return self.of(RetCode.IMAGE_SOP_INSTANCE_UID_IS_EMPTY)
        # 忽略算法结果
        if tags.get("Manufacturer", "") == Const.ALGORITHM_RESULT_MANUFACTURER:
            log.info("Image[callback] > study:{}, series:{}, image:{}, ignore result callback".format(
                study_instance_uid, series_instance_uid, sop_instance_uid))
            return self.ok(message="result callback")
        # 忽略重复图像
        dicom_queryset = CallBackDICOM.objects.filter(sop_instance_uid=sop_instance_uid)
        if dicom_queryset.exists():
            log.info("Image[callback] > study:{}, series:{}, sop:{}, already exists".format(
                study_instance_uid, series_instance_uid, sop_instance_uid))
            return self.of(RetCode.IMAGE_ALREADY_EXISTS)
        callback_service = ImageCallbackService(image_orthanc_id, tags, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        return self.of(ret_code)

