#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import traceback
import uuid
import logging

import requests
from pymongo import MongoClient
from django.db import connection

from server import settings
from server.mails.models import VersionModel


log = logging.getLogger("django")


class Version:

    # third-part
    def get_mysql_version(self):
        """
        获取mysql数据库版本
        """
        version = ""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                row = cursor.fetchone()
                version = row[0]
        except:
            log.error(f"Version get mysql error: {traceback.format_exc()}")
        return "ugs-mysql", version

    def get_mongo_version(self):
        """
        获取mongo数据库版本
        """
        version = ""
        try:
            client = MongoClient(
                host=settings.MONGODB_HOST,
                port=int(settings.MONGODB_PORT),
                username=settings.MONGODB_USER,
                password=settings.MONGODB_PASSWORD
            )
            version = client.server_info()["version"]
        except:
            log.error(f"Version get mysql error: {traceback.format_exc()}")
        return "ugs-mongodb", version

    def get_rabbitmq_version(self):
        """
        获取rabbitmq版本
        """
        MQ_USERNAME = settings.MQ_USERNAME
        MQ_PASSWORD = settings.MQ_PASSWORD
        MQ_HOST = settings.MQ_HOST
        MQ_WEB_PORT = settings.MQ_WEB_PORT
        url = f"http://{MQ_HOST}:{MQ_WEB_PORT}/api/all-configuration"
        auth = (MQ_USERNAME, MQ_PASSWORD)
        version = ""
        try:
            response = requests.get(url, auth=auth)
            if response.status_code == 200:
                version = response.json().get("rabbit_version")
        except:
            log.error(f"Version get mysql error: {traceback.format_exc()}")
        return "ugs-rabbitmq", version

    def get_orthanc_version(self):
        """
        获取rabbitmq版本
        """
        version = ""
        try:
            ORTHANC_WADO_USERNAME = settings.ORTHANC_WADO_USERNAME
            ORTHANC_WADO_PASSWORD = settings.ORTHANC_WADO_PASSWORD
            ORTHANC_HOST = settings.ORTHANC_HOST
            ORTHANC_WEB_PORT = settings.ORTHANC_WEB_PORT
            url = f"http://{ORTHANC_HOST}:{ORTHANC_WEB_PORT}/system"
            auth = (ORTHANC_WADO_USERNAME, ORTHANC_WADO_PASSWORD)
            response = requests.get(url, auth=auth)
            if response.status_code == 200:
                version = response.json().get("Version")
        except:
            log.error(f"Version get mysql error: {traceback.format_exc()}")
        return "ugs-pacs", version

    # 后端服务
    def get_backend_version(self):
        """
        获取后台版本
        """
        version = settings.API_VERSION
        return "ugs-api", version

    def create_update_version(self, server_type, version):
        """
        更新版本信息
        """
        try:
            queryset = VersionModel.objects.filter(server_type=server_type, is_delete=False)
            if queryset.exists():
                queryset.update(**{"version": version})
                log.info("The version update success. server_type:{}, version:{}".format(server_type, version))
            else:
                data = {
                    "uuid": uuid.uuid1(),
                    "version": version,
                    "server_type": server_type
                }
                VersionModel.objects.create(**data)
                log.info("The version create success. server_type:{}, version:{}".format(server_type, version))
        except Exception as e:
            log.error("update or create version error: %s", str(e))

    def update_version_init(self):
        """
        服务初始化的时候更新版本信息
        """
        version_list = [
            self.get_rabbitmq_version(),
            self.get_mysql_version(),
            self.get_mongo_version(),
            self.get_orthanc_version(),
            self.get_backend_version(),
        ]
        for service_name, version in version_list:
            self.create_update_version(server_type=service_name, version=version)

    def get_version_list(self):
        """
        获取服务版本接口
        """
        version_qs = VersionModel.objects.filter(is_delete=False).values("server_type", "version")
        result_map = {
            "后端服务版本号": [],
            "算法服务版本号": [],
            "第三方服务版本号": [],
        }
        for version_value in version_qs:
            server_type = version_value.get('server_type')
            version = version_value.get('version')
            server_name = settings.monitor_list.get(server_type, "")
            if not server_name:
                continue
            data = {
                "server_name": server_name,
                "server_type": server_type,
                "version": version,
            }
            if server_type in ["ugs-api", "ugs-transport", "ugs-result", "ugs-delete"]:
                result_map["后端服务版本号"].append(data)
            elif server_type in ["ugs-mysql", "ugs-rabbitmq", "ugs-mongodb", "ugs-pacs"]:
                result_map["第三方服务版本号"].append(data)
            elif server_type in ["ugs-aspects", "ugs-ctp", "ugs-cta"]:
                result_map["算法服务版本号"].append(data)
        result = []
        for key, value in result_map.items():
            result.append({
                "category": key,
                "versions": value
            })
        return result

