# Generated by Django 2.0.5 on 2021-11-02 12:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AlgorithmTypeModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('type', models.CharField(max_length=255, verbose_name='类型')),
                ('keyword', models.CharField(blank=True, max_length=255, null=True, verbose_name='关键字')),
                ('value', models.CharField(blank=True, max_length=255, null=True, verbose_name='值')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_timestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('is_delete', models.BooleanField(default=False, verbose_name='是否删除')),
            ],
            options={
                'verbose_name': '算法分类表',
                'verbose_name_plural': '算法分类表',
                'db_table': 'algorithm_type',
            },
        ),
        migrations.CreateModel(
            name='MailAutoModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('type', models.CharField(max_length=255, verbose_name='类型')),
                ('status', models.CharField(blank=True, max_length=255, null=True, verbose_name='状态')),
                ('auto', models.IntegerField(choices=[(0, '手动'), (1, '自动')], default=0, verbose_name='自动发送')),
                ('method', models.IntegerField(choices=[(0, '文字'), (1, '图片'), (2, '文字+图片')], default=0, verbose_name='发送类型')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_timestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('is_delete', models.BooleanField(default=False, verbose_name='是否删除')),
            ],
            options={
                'verbose_name': '邮件自动发送表',
                'verbose_name_plural': '邮件自动发送表',
                'db_table': 'mail_auto',
            },
        ),
        migrations.CreateModel(
            name='MailConfigModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('address', models.CharField(blank=True, max_length=255, null=True, verbose_name='邮箱地址')),
                ('password', models.CharField(blank=True, max_length=255, null=True, verbose_name='密码')),
                ('type', models.CharField(choices=[('send', '发信箱'), ('receive', '收信箱')], default='receive', max_length=255, verbose_name='类型')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_timestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('is_delete', models.BooleanField(default=False, verbose_name='是否删除')),
            ],
            options={
                'verbose_name': '邮箱配置表',
                'verbose_name_plural': '邮箱配置表',
                'db_table': 'mail_config',
            },
        ),
        migrations.CreateModel(
            name='MailHistoryModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('send_address', models.CharField(blank=True, max_length=255, null=True, verbose_name='发件地址')),
                ('receive_address', models.CharField(blank=True, max_length=255, null=True, verbose_name='收件地址')),
                ('send_time', models.DateTimeField(auto_now_add=True, verbose_name='发送时间')),
                ('send_status', models.IntegerField(choices=[(0, '失败'), (1, '成功')], default=0, verbose_name='发送状态')),
                ('object_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='发送内容')),
                ('message', models.CharField(blank=True, max_length=255, null=True, verbose_name='发送错误信息')),
                ('theme', models.CharField(blank=True, max_length=255, null=True, verbose_name='主题')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_timestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('is_delete', models.BooleanField(default=False, verbose_name='是否删除')),
            ],
            options={
                'verbose_name': '邮件发送历史表',
                'verbose_name_plural': '邮件发送历史表',
                'db_table': 'mail_history',
            },
        ),
        migrations.CreateModel(
            name='ProcessUnusualDataModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('study_instance_uid', models.TextField(blank=True, null=True, verbose_name='STUDY序列uid')),
                ('series_instance_uid', models.TextField(blank=True, null=True, verbose_name='Series序列uid')),
                ('patient_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='病人id')),
                ('patient_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='病人姓名')),
                ('updatetimestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('number', models.IntegerField(default=0, verbose_name='series下文件数量')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('study_description', models.TextField(blank=True, null=True, verbose_name='Study描述')),
                ('modality', models.TextField(blank=True, null=True, verbose_name='设备类型')),
                ('series_description', models.TextField(blank=True, null=True, verbose_name='series 描述')),
                ('protocol_name', models.TextField(blank=True, null=True, verbose_name='协议名')),
                ('slice_thickness', models.TextField(blank=True, null=True, verbose_name='厚度')),
                ('task_uuid', models.CharField(blank=True, max_length=255, null=True, verbose_name='任务id')),
                ('finish_percent', models.IntegerField(default=0, verbose_name='完成百分比')),
                ('algorithm_type', models.CharField(blank=True, max_length=255, null=True, verbose_name='算法类型')),
                ('download_status', models.BooleanField(default=False, verbose_name='下载状态')),
                ('download_result', models.CharField(blank=True, max_length=255, null=True, verbose_name='下载结果')),
                ('download_error', models.CharField(blank=True, max_length=255, null=True, verbose_name='失败原因')),
            ],
            options={
                'verbose_name': '处理异常数据',
                'verbose_name_plural': '处理异常数据',
                'db_table': 'process_unusual_data',
            },
        ),
    ]
