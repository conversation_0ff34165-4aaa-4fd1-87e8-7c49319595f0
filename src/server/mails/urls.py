from django.urls import include, path

from .views import MailConfigView, MailAutoView, SendEmailView, RemindView, DownloadView, UploadFileView, \
    UploadFilesView, DesktopQueryFilesView, VersionView

urlpatterns = [
    # 邮件配置，查询，更新
    path('config/', MailConfigView.as_view()),
    # 邮件删除
    path('config/<pk>', MailConfigView.as_view()),
    # 配置邮件发送模式
    path("method/", MailAutoView.as_view()),
    # 发送邮件
    path('send/', SendEmailView.as_view()),
    # 提醒
    path("remind/", RemindView.as_view()),
    # 下载文件
    path("download/", DownloadView.as_view()),
    # 桌面端算法结果单文件回传接口
    path("upload/", UploadFileView.as_view()),
    # 桌面端算法结果多文件回传接口
    path("uploads/", UploadFilesView.as_view()),
    # 桌面端算法结果查询接口
    path("result/", DesktopQueryFilesView.as_view()),
    # 查询服务版本的接口
    path("version/", VersionView.as_view()),

]
