import datetime
import importlib
import traceback
import uuid

from django.core.validators import validate_email, ValidationError
from django.http import JsonResponse
from django.shortcuts import HttpResponse

from server import settings
from server.algorithm.models import AlgorithmTask
get_cpu_capacity = importlib.import_module('server.async.utils').get_cpu_capacity
from server.common.views import BaseView
from server.mails.models import MailConfigModel, MailAutoModel
from server.mails.service import SendMailByStudyUID
from server.mails.utils import FileUtil
from server.user.models import AuthorizationCode
from server.mails.version_tools import Version
from ..common.base import check_auth_code
from ..common.code import RetCode
from ..study.models import Study


# Create your views here.
from ..user.auth import AuthVerify


class MailConfigView(BaseView):

    def get(self, request):
        """
        查询邮箱
        """
        response = self.response
        # creator = check_auth_code(request)
        # if not creator:
        #     response['code'] = 413
        #     response['message'] = '操作验证失败'
        #     return JsonResponse(response)
        send_queryset = MailConfigModel.objects.filter(is_delete=False, type="send")
        receive_queryset = MailConfigModel.objects.filter(is_delete=False, type="receive").order_by("timestamp")
        data = {
            "send": {},
            "receive": [],
        }
        if send_queryset:
            data['send'] = send_queryset.first().to_dict()
        if receive_queryset:
            for receive in receive_queryset:
                data["receive"].append(receive.to_dict())
        response["message"] = "查询成功"
        response["code"] = 200
        response["data"] = data
        response["status"] = True
        return JsonResponse(response)

    def post(self, request):
        """
        配置邮箱
        """
        response = self.response
        # creator = check_auth_code(request)
        # if not creator:
        #     response['code'] = 413
        #     response['message'] = '操作验证失败'
        #     return JsonResponse(response)
        data = request.POST
        mail_type = data.get("mail_type", None)
        address = data.get("email", None)
        password = data.get("password", None)
        if mail_type not in ["send", "receive"]:
            response["message"] = "请选择正确的邮件类型"
            return JsonResponse(response)
        if not address:
            response["message"] = "请输入邮箱地址"
            return JsonResponse(response)
        # 验证邮箱格式
        status = self.validate_email(address)
        if not status:
            response["message"] = "请输入正确的邮箱"
            return JsonResponse(response)
        # 判断发信箱，1判断地址是否存在，存在更新密码，不存在创建邮箱和密码
        if mail_type == "send":
            if not password:
                response["message"] = "请输入邮箱密码"
                return JsonResponse(response)
            mail_config_queryset = MailConfigModel.objects.filter(type="send", is_delete=False)
            if not mail_config_queryset:
                # TODO 密码加密
                mail_data = {
                    "address": address,
                    "password": password,
                    "type": "send",
                    "uuid": str(uuid.uuid1())
                }
                MailConfigModel.objects.create(**mail_data)
                response["code"] = 200
                response["status"] = True
                response["message"] = "创建邮箱成功"
                return JsonResponse(response)
            else:
                mail_config_queryset.update(**{"address": address, "password": password})
                response["code"] = 200
                response["status"] = True
                response["message"] = "更新邮箱成功"
                return JsonResponse(response)
        else:
            mail_config_queryset = MailConfigModel.objects.filter(address=address, type="receive", is_delete=False)
            if mail_config_queryset:
                response["message"] = "此邮箱已存在"
                return JsonResponse(response)
            mail_data = {
                "address": address,
                "type": "receive",
                "uuid": str(uuid.uuid1())
            }
            MailConfigModel.objects.create(**mail_data)
            response["code"] = 200
            response["status"] = True
            response["message"] = "创建邮箱成功"
            return JsonResponse(response)

            # 多邮箱创建
            # mail_config_queryset.update(is_delete=True)
            # mail_list = []
            # for i in address:
            #     mail_data = MailConfigModel(uuid=str(uuid.uuid1()), address=i, type="receive")
            #     mail_list.append(mail_data)
            # if mail_list:
            #     MailConfigModel.objects.bulk_create(mail_list)
            #     response["code"] = 200
            #     response["status"] = True
            #     response["message"] = "更新邮箱成功"
            #     return JsonResponse(response)
            # else:
            #     response["message"] = "更新邮箱失败"
            #     return JsonResponse(response)

    def delete(self, request, pk):
        """
        删除邮箱
        """
        response = self.response
        # creator = check_auth_code(request)
        # if not creator:
        #     response['code'] = 413
        #     response['message'] = '操作验证失败'
        #     return JsonResponse(response)
        if not pk:
            response['code'] = 413
            response['message'] = "参数传入错误"
            return JsonResponse(response)
        queryset = MailConfigModel.objects.filter(uuid=pk, is_delete=False)
        if not queryset:
            response['message'] = "没有此邮箱id"
            return JsonResponse(response)
        queryset.update(is_delete=True)
        response["message"] = "删除成功"
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)

    @staticmethod
    def validate_email(email):
        """邮件格式验证"""
        try:
            validate_email(email)
            return True
        except ValidationError:
            return False

    def validate_emails(self, emails):
        """
        验证邮箱列表格式
        """
        status = True
        for email in emails:
            _status = self.validate_email(email)
            if not _status:
                status = False
                break
        return status


class MailAutoView(BaseView):

    def get(self, request):
        """
        查询邮件发送模式
        """
        response = self.response
        # creator = check_auth_code(request)
        # if not creator:
        #     response['code'] = 413
        #     response['message'] = '操作验证失败'
        #     return JsonResponse(response)
        queryset = MailAutoModel.objects.all()
        # 没有配置默认创建一天新的，默认不自动发送，文字
        config = [{i[0]: i[1]} for i in MailAutoModel.METHOD_CHOICES]
        if not queryset:
            auto_data = {
                "uuid": str(uuid.uuid1()),
                "type": "email",
                "auto": 0,
                "method": 0
            }
            MailAutoModel.objects.create(**auto_data)
            response["data"] = {"auto": 0, "method": "word", "config": config}
            response["code"] = 200
            response["message"] = "查询成功"
            response["status"] = True
            return JsonResponse(response)
        else:
            _queryset = queryset.first()
            data = {"auto": _queryset.auto,
                    "method": _queryset.method,
                    "config": config
                    }
            response["data"] = data
            response["code"] = 200
            response["message"] = "查询成功"
            response["status"] = True
            return JsonResponse(response)

    def post(self, request):
        """
        设置邮件发送模式
        """
        response = self.response
        # creator = check_auth_code(request)
        # if not creator:
        #     response['code'] = 413
        #     response['message'] = '操作验证失败'
        #     return JsonResponse(response)
        data = request.POST
        auto = data.get("auto", None)
        method = data.get("method", None)
        queryset = MailAutoModel.objects.all()
        if str(auto).isdigit() and not method:
            if not queryset:
                auto_data = {
                    "uuid": str(uuid.uuid1()),
                    "type": "email",
                    "auto": auto,
                    "method": 0
                }
                MailAutoModel.objects.create(**auto_data)
            else:
                queryset.update(auto=auto)
            response["message"] = "更新配置成功"
            response["code"] = 200
            response["status"] = True
            return JsonResponse(response)
        elif str(method).isdigit() and not auto:
            if not queryset:
                auto_data = {
                    "uuid": str(uuid.uuid1()),
                    "type": "email",
                    "auto": 0,
                    "method": method
                }
                MailAutoModel.objects.create(**auto_data)
            else:
                queryset.update(method=method)
            response["message"] = "更新配置成功"
            response["code"] = 200
            response["status"] = True
            return JsonResponse(response)
        else:
            response["message"] = "请求参数错误"
            return JsonResponse(response)


class SendEmailView(BaseView):

    def post(self, request):
        """
        发送邮件
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        data = request.POST
        send = SendMailByStudyUID()
        StudyUid = data.get("StudyUid", None)
        algorithm_type = data.get("algorithm_type", None)
        is_auto = data.get("auto", False)
        if not StudyUid:
            return self.fail(message="StudyUid不能为空")
        if not algorithm_type:
            return self.fail(message="算法类型不能为空")
        study = Study.objects.filter(study_instance_uid=StudyUid).first()
        if not study:
            return self.fail(message="无效StudyUid")
        series_uid_list = [series.series_instance_uid for series in study.series_set.all()]
        queryset = AlgorithmTask.objects.filter(series_uid__in=series_uid_list, finish_percent__isnull=False)
        if not queryset:
            return self.fail(message="没有此任务，无法发送邮件")
        _queryset = queryset.first()
        percent = _queryset.finish_percent
        if percent > 100:
            return self.fail(message="算法失败，无法发送邮件")
        if not is_auto and percent < 100:
            return self.fail(message="算法计算中，无法发送邮件")
        status, message = send.send_mail_by_study(StudyUid, algorithm_type)
        if not status:
            return self.fail(message=message)
        return self.ok(message="发送成功")


class RemindView(BaseView):
    def get(self, request):
        response = self.response
        cup_data = get_cpu_capacity()
        status = False
        # 判断cup使用是否超80%
        if cup_data:
            used_percent = cup_data.get("used_percent", 0)
            if str(used_percent).isdigit():
                if int(used_percent) >= 80:
                    status = True
        creator, end_date, auth_module_list = AuthVerify().get_auth_code_and_module()
        now_time = datetime.datetime.now()
        total_sec = (end_date - now_time).days
        if total_sec <= 30:
            status = True
        data = {
            "authorization": {
                "auth_end_date": end_date.strftime("%Y-%m-%d"),
                "days": total_sec,
                "modules": auth_module_list
            },
            "cup_data": cup_data,
            "status": status
        }
        response["data"] = data
        response["message"] = "登录成功"
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class DownloadView(BaseView):
    def get(self, request):
        response = self.response
        data = request.GET
        file_name = data.get("file")
        if not file_name:
            return self.fail(412, "请出入文件名称")
        file_path = os.path.join("/code/data/cta/zip", file_name)
        if os.path.exists(file_path):
            file = open(file_path, 'rb')
            response = HttpResponse(file)
            response['Content-Type'] = 'application/octet-stream'  # 设置头信息，告诉浏览器这是个文件
            response['Content-Disposition'] = 'attachment;filename="' + os.path.basename(file_name) + '"'
            return response
        else:
            return self.fail(412, "没有此文件")


import os
import zipfile


def zip_dir(dirname, zip_file_name):
    file_list = []
    path = os.path.join(dirname)
    if os.path.isfile(dirname):
        file_list.append(dirname)
    else:
        for root, dirs, files in os.walk(dirname):
            for name in files:
                file_list.append(os.path.join(root, name))
    zf = zipfile.ZipFile(zip_file_name, "w", zipfile.zlib.DEFLATED)
    for tar in file_list:
        zf_name = tar[len(dirname):]
        # print arcname
        zf.write(tar, zf_name)
    zf.close()


class UploadFileView(BaseView):
    def post(self, request):
        base_dir = FileUtil.get_unified_path(os.path.join(settings.DOCKER_DATA_BASE_DIR, "cta/zip"))
        # 文件夹不存在则创建
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        try:
            file = request.body
            # 校验请求参数
            get_req = request.GET
            series_instance_uid = get_req.get('series', "")
            if not series_instance_uid:
                return self.fail(412, "missing parameter: series")
            file_name = get_req.get('file_name', "")
            if not file_name:
                return self.fail(412, "missing parameter: file_name")
            # 文件保存目录
            save_dir = FileUtil.get_unified_path(os.path.join(base_dir, series_instance_uid, "desktop"))
            if not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)
            dt = datetime.datetime.now()
            new_file_name = F"{dt.strftime('%Y%m%d%H%M%S%f')}_{file_name}"
            file_save_path = FileUtil.get_unified_path(os.path.join(save_dir, new_file_name))
            with open(file_save_path, 'wb') as fp:
                fp.write(file)
            print("series[{}] > save {} to {}".format(series_instance_uid, file_name, file_save_path))
            return self.ok()
        except Exception as e:
            print(traceback.format_exc())
            return self.fail(412, "upload file error")


class UploadFilesView(BaseView):
    """文件上传视图"""

    def post(self, request):
        """
        文件上传接口

        :param request:
            series(URL): 序列UID
            file(Body): 上传文件（支持多个）
        :return:
        """
        base_dir = FileUtil.get_unified_path(os.path.join(settings.DOCKER_DATA_BASE_DIR, "cta/zip"))
        # 文件夹不存在则创建
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        try:
            # 校验请求参数
            get_req = request.GET
            series_instance_uid = get_req.get('series', "")
            if not series_instance_uid:
                return self.fail(412, "missing parameter: series")
            files = request.FILES.getlist("file")
            if not files or len(files) <= 0:
                return self.fail(412, "missing parameter: file")
            print("series[{}] > upload {} files".format(series_instance_uid, len(files)))
            # 文件保存目录
            save_dir = FileUtil.get_unified_path(os.path.join(base_dir, series_instance_uid, "desktop"))
            if not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)
            # 保存上传文件
            for file in files:
                dt = datetime.datetime.now()
                new_file_name = F"{dt.strftime('%Y%m%d%H%M%S%f')}_{file.name}"
                file_save_path = FileUtil.get_unified_path(os.path.join(save_dir, new_file_name))
                print("series[{}] > save {} to {}".format(series_instance_uid, file.name, file_save_path))
                fb = open(file_save_path, 'wb+')
                for chunk in file.chunks():
                    fb.write(chunk)
                fb.close()
            return self.ok()
        except Exception as e:
            print(traceback.format_exc())
            return self.fail(412, "upload file error")


class DesktopQueryFilesView(BaseView):
    """桌面端结果查询视图"""

    def get(self, request):
        try:
            # print("path:", request.get_full_path(), request.path, request.path_info)
            # print("request get_host:", request.get_host())
            # print("request build_absolute_uri:", request.build_absolute_uri())
            # print("Meta[REMOTE_ADDR]:", request.META.get("REMOTE_ADDR"))
            # print("Meta[HTTP_HOST]:", request.META.get("HTTP_HOST"))
            # print("Meta[HTTP_X_FORWARDED_FOR]:", request.META.get('HTTP_X_FORWARDED_FOR'))
            # print("Meta:", request.META)
            # 校验请求参数
            get_req = request.GET
            series_instance_uid = get_req.get('series', "")
            if not series_instance_uid:
                return self.fail(412, "missing parameter: series")
            base_dir = FileUtil.get_unified_path(os.path.join(settings.DOCKER_DATA_BASE_DIR, "cta/zip"))
            data = {"modified": False}
            series_dir_path = FileUtil.get_unified_path(os.path.join(base_dir, series_instance_uid))
            if os.path.exists(F"{series_dir_path}.zip"):
                data["original"] = request.build_absolute_uri(F"/api/v1/mail/download/?file={series_instance_uid}.zip")
            desktop_path = FileUtil.get_unified_path(os.path.join(series_dir_path, "desktop"))
            if os.path.exists(desktop_path):
                results = []
                for root, dirs, files in os.walk(desktop_path):
                    for file_name in files:
                        download_path = F"{series_instance_uid}/desktop/{file_name}"
                        results.append(request.build_absolute_uri(F"/api/v1/mail/download/?file={download_path}"))
                if len(results) > 0:
                    data["results"] = results
                    data["modified"] = True
            return self.ok(data)
        except:
            print(traceback.format_exc())
            return self.fail(412, "query error")


class VersionView(BaseView):
    def get(self, request):
        """
        获取服务版本接口
        """
        result = Version().get_version_list()
        return self.ok(data=result)



