"""
WSGI config for server project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")

application = get_wsgi_application()

from server.user.auth import AuthVerify
from server.mails.version_tools import Version

# 启动时候获取机器码
AuthVerify().get_machine_code()
# 持久化后台服务的版本
Version().update_version_init()
