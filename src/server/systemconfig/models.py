import datetime
from django.db import models
# Create your models here.


class SystemConfig(models.Model):
    id = models.CharField(primary_key=True, max_length=255)   # ID
    code = models.CharField(max_length=64, unique=True, null=False, verbose_name="配置项编码")
    value = models.CharField(max_length=512, null=False, default="", verbose_name="配置项值")
    default_value = models.CharField(max_length=512, null=False, default="", verbose_name="配置项默认值")
    format = models.CharField(max_length=8, null=False, default="text", verbose_name="属性值格式（text、bool、int、float、json）")
    category = models.CharField(max_length=32, null=False, default="", verbose_name="分类（ctp、aspects、cta）")
    tag = models.CharField(max_length=32, null=False, default="", verbose_name="标签")
    custom = models.BooleanField(null=False, default=False, verbose_name="自定义配置")
    description = models.CharField(max_length=64, null=False, default="", verbose_name="配置描述")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    class Meta:
        db_table = 't_config'
        verbose_name = '系统配置表'
        verbose_name_plural = 'systemconifg'

    def to_dict(self):
        return dict(
            id=self.id,
            code=self.code,
            value=self.value,
            defaultValue=self.default_value,
            format=self.format,
            category=self.category,
            custom=self.custom,
            description=self.description,
            gmt_create=self.gmt_create.strftime('%Y-%m-%d %H:%M:%S'),
            gmt_modified=self.gmt_modified.strftime('%Y-%m-%d %H:%M:%S'),
        )

    def short_dict(self):
        return dict(
            id=self.id,
            code=self.code,
            value=self.value,
            defaultValue=self.default_value,
            format=self.format,
            category=self.category,
            custom=self.custom,
            description=self.description
        )

