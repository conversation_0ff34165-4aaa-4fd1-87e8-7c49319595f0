import traceback
import uuid

from django.db.models import F
from django.http import JsonResponse
from server.common.base import check_auth_code
from server.common.code import RetCode
# Create your views here.
from server.common.views import BaseView
from server.systemconfig.models import SystemConfig

import logging

from server.systemconfig.system_config_utils import SystemConfigUtils

log = logging.getLogger("django")


class SystemConfigView(BaseView):
    """ 获取系统配置 """

    def get(self, request):
        # 权限验证
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)

        data = request.GET
        queryset = SystemConfig.objects.all()
        if data:
            # 配置项编码查询
            query_code = data.get('code', '')
            if query_code:
                queryset = queryset.filter(code=query_code)

            query_type = data.get('type', '')
            if query_type:
                queryset = queryset.filter(category=query_type)
            # 扩展查询
            code_list = data.getlist("codes", [])
            if code_list:
                queryset = queryset.filter(code__in=code_list)
            tag = data.get("tag", "")
            if tag:
                queryset = queryset.filter(tag=tag)
            custom = data.get("custom", "")
            if custom:
                queryset = queryset.filter(custom=custom)
            log.info("[SystemConfigView] > get SystemConfigsBy code: %s, type: %s", query_code, query_type)

        return_data = [query.short_dict() for query in queryset.all()]

        response = self.response
        response['status'] = True
        response['code'] = 200
        response['data'] = return_data
        response['message'] = '获取系统配置成功'
        return JsonResponse(response)

    """ 更新系统配置 """

    def post(self, request):
        response = self.response
        # 权限验证
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)

        log.info("[SystemConfigView] > update SystemConfigs")
        data = request.POST
        if data:
            sysconfigs = data.get("data", [])
            for sysconfig in sysconfigs:
                config = SystemConfig.objects.filter(code=sysconfig.get("code")).first()
                if config:
                    log.info("[SystemConfigView] > update SystemConfigs set %s = %s", config.code,
                             sysconfig.get("value"))
                    config.value = sysconfig.get("value")
                    config.save()
                else:
                    response['code'] = 413
                    response['message'] = '无效的配置项编码'
                    return JsonResponse(response)

        response['status'] = True
        response['code'] = 200
        response['data'] = ""
        response['message'] = '更新成功'
        return JsonResponse(response)


class CustomConfigView(BaseView):

    def post(self, request):
        # 权限验证
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        data = request.POST
        _code = data.get('code', '')
        _value = data.get('value', '')
        _format = data.get('format', '')
        _category = data.get('category', '')
        _tag = data.get('tag', '')
        _desc = data.get('desc', '')
        if not _code or not _value or not _format or not _category or not _desc:
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        config = SystemConfig.objects.filter(code=_code).first()
        if config:
            return self.of(RetCode.CONFIG_DUPLICATE_CODE)
        SystemConfig.objects.create(id=str(uuid.uuid1()), code=_code, value=_value, default_value=_value,
                                    format=_format, category=_category, tag=_tag, custom=True, description=_desc)
        return self.ok()

    def delete(self, request):
        # 权限验证
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        data = request.GET
        _code = data.get('code', '')
        if not _code:
            return self.of(RetCode.CONFIG_CODE_IS_EMPTY)
        config = SystemConfig.objects.filter(code=_code).first()
        if not config:
            return self.of(RetCode.CONFIG_INVALID_CODE)
        if not config.custom:
            return self.of(RetCode.CONFIG_CODE_IS_NOT_CUSTOM)
        config.delete()
        return self.ok()


class ResetConfigView(BaseView):

    def post(self, request):
        """
        页面admin恢复默认配置： 页面点击后需要把所有数据进行出厂设置 即 把default_value传给value
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        try:
            SystemConfig.objects.update(value=F('default_value'))
        except:
            log.info("[ResetConfigView] > error %", traceback.format_exc())
            return self.fail(400, "恢复默认配置异常")
        return self.ok()


class SystemConfView(BaseView):

    def get(self, request):
        """
        type inspection
        """
        data = request.GET
        code = data.get("code", "typeInspection")
        type_inspection = SystemConfigUtils().getConfigEvalValue(code=code, def_value=False)
        return JsonResponse({"value": type_inspection})

