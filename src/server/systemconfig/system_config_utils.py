import traceback

from server.systemconfig.models import SystemConfig
from ast import literal_eval
import logging
log = logging.getLogger("django")


class SystemConfigUtils:

    """ 根据{code}获取系统配置值，
        如果未查询到或者查询异常，返回默认值{def_value}
    """
    def getConfigValue(self, code, def_value):

        try:
            config = SystemConfig.objects.filter(code=code).first()
            if config:
                log.debug("[SystemConfig] > code: {}, value: {}, def_value: {}".format(code, config.value, def_value))
                return config.value
        except Exception:
            log.error("[SystemConfig] > failed to get config, code:{}, default:{}, {}".format(
                code, def_value, traceback.format_exc()))
            return def_value
        log.debug("[SystemConfig] > {} not found, default: {}".format(code, def_value))
        return def_value

    def getConfigEvalValue(self, code, def_value):

        try:
            config = SystemConfig.objects.filter(code=code)
            if config.exists():
                value = config.first().value
                log.debug("[SystemConfig] > code: {}, value: {}, def_value: {}".format(code, value, def_value))
                result = literal_eval(value)
                return result
        except Exception:
            log.error("[SystemConfig] > failed to get config, code:{}, default:{}, {}".format(
                code, def_value, traceback.format_exc()))
            return def_value
        log.debug("[SystemConfig] > {} not found, default: {}".format(code, def_value))
        return def_value

    @staticmethod
    def get_config(code, def_value):
        try:
            config = SystemConfig.objects.filter(code=code).first()
            if not config:
                return def_value
            log.debug("Config > code: {}, value: {}, def_value: {}".format(code, config.value, def_value))
            return config.value
        except Exception:
            log.error("Config > failed to get config, code:{}, default:{}, {}".format(
                code, def_value, traceback.format_exc()))
            return def_value

