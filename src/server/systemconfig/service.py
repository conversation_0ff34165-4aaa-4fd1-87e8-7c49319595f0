#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : ugs-api
<AUTHOR> mingxing
@Date    : 2023/5/31 16:56
"""
import datetime
import logging
import os.path

log = logging.getLogger("django")


class LogQueryHandler:
    LOG_ROOT = "/code/data/log"
    DT_FORMAT_PLATFORM = "%Y-%m-%d %H:%M:%S"
    DT_FORMAT_CTA = "%m/%d/%y %H:%M:%S"

    def __init__(self, service_code, start_time, end_time, key_word, page_no, page_size):
        self.service_code = service_code
        self.start_time = start_time
        self.end_time = end_time
        self.key_word = key_word
        self.start_index = (page_no - 1) * page_size
        self.end_index = page_no * page_size
        self.page_size = page_size

    def find(self):
        service_log_dir = os.path.join(self.LOG_ROOT, self.service_code)
        if not os.path.exists(service_log_dir):
            log.info("Log[{}] > logger not found".format(self.service_code))
            return []
        # 获取日志文件（按时间排序）
        file_name_list = [f for f in os.listdir(service_log_dir) if not f.startswith(".")]
        file_name_list = sorted(file_name_list, reverse=True)
        log.info("Log[{}] > files:{}".format(self.service_code, file_name_list))
        match_count = 0
        data = []
        # 特殊处理：CTP/CTA日志需要合并算法日志
        if self.service_code in ["ugs-cta", "ugs-ctp"]:
            return self.get_algorithm_logs()
        for file_name in file_name_list:
            file_path = os.path.join(service_log_dir, file_name)
            log.info("Log[{}] > handle {}".format(self.service_code, file_path))
            with open(file_path) as f:
                line_list = f.readlines()
                min_datetime = self.get_min_datetime(line_list)
                max_datetime = self.get_max_datetime(line_list)
                log.info("Log[{}] > file:{}, search:{}~{}, logger:{}~{}".format(
                    self.service_code, file_name, self.start_time, self.end_time, min_datetime, max_datetime))
                # 日志命中时间区间
                if (min_datetime <= self.start_time and self.end_time <= max_datetime) \
                        or (self.start_time <= min_datetime <= self.end_time) \
                        or (self.start_time <= max_datetime <= self.end_time):
                    log.info("Log[{}] > get data from {}, index:{}~{}".format(
                        self.service_code, file_name, self.start_index, self.end_index))
                    for line in line_list:
                        data_size = len(data)
                        if data_size >= self.page_size:
                            return data
                        if self.service_code not in line:
                            if match_count > 1:
                                data.append(line)
                                match_count += 1
                            continue
                        log_datetime = self.parse_datetime(line[:19])
                        if (self.start_time <= log_datetime <= self.end_time) \
                                and (not self.key_word or (self.key_word and self.key_word in line)):
                            if self.start_index <= match_count <= self.end_index:
                                data.append(line)
                            match_count += 1
        return data

    def get_min_datetime(self, line_list):
        for line in line_list:
            if self.service_code in line:
                min_time = self.parse_datetime(line[:19])
                if min_time:
                    return min_time

    def get_max_datetime(self, line_list):
        for line in line_list[::-1]:
            if self.service_code in line:
                max_time = self.parse_datetime(line[:19])
                if max_time:
                    return max_time

    @staticmethod
    def parse_datetime(datetime_str, _format=DT_FORMAT_PLATFORM):
        try:
            return datetime.datetime.strptime(datetime_str, _format)
        except:
            return None

    def get_algorithm_logs(self):
        log_dict = dict()
        # 集成日志
        service_log_dir = os.path.join(self.LOG_ROOT, self.service_code)
        file_name_list = [f for f in os.listdir(service_log_dir) if not f.startswith(".")]
        file_name_list = sorted(file_name_list, reverse=True)
        for file_name in file_name_list:
            file_path = os.path.join(service_log_dir, file_name)
            log.info("Log[{}] > handle {}".format(self.service_code, file_path))
            with open(file_path) as f:
                line_list = f.readlines()
                min_datetime = self.get_min_datetime(line_list)
                max_datetime = self.get_max_datetime(line_list)
                log.info("Log[{}] > file:{}, search:{}~{}, logger:{}~{}".format(
                    self.service_code, file_name, self.start_time, self.end_time, min_datetime, max_datetime))
                if min_datetime is None or max_datetime is None:
                    continue
                # 日志命中时间区间
                if (min_datetime <= self.start_time and self.end_time <= max_datetime) \
                        or (self.start_time <= min_datetime <= self.end_time) \
                        or (self.start_time <= max_datetime <= self.end_time):
                    log.info("Log[{}] > get data from {}, index:{}~{}".format(
                        self.service_code, file_name, self.start_index, self.end_index))
                    previous_key = None
                    for line in line_list:
                        log_datetime = self.parse_datetime(line[:19])
                        if not log_datetime:
                            if previous_key:
                                log_dict[previous_key].append(line)
                            continue
                        if (self.start_time <= log_datetime <= self.end_time) \
                                and (not self.key_word or (self.key_word and self.key_word in line)):
                            key = int(log_datetime.timestamp())
                            if key not in log_dict:
                                log_dict[key] = []
                            if not self.key_word or self.key_word in line:
                                log_dict[key].append(line)
                                previous_key = key
        # 算法日志
        log_dir = os.path.join(self.LOG_ROOT, "alg-{}".format(self.service_code[4:]))
        file_name_list = sorted(os.listdir(log_dir), reverse=True)
        for file_name in file_name_list:
            file_path = os.path.join(log_dir, file_name)
            log.info("Log[{}] > handle {}".format(self.service_code, file_path))
            with open(file_path) as f:
                line_list = f.readlines()
                for line in line_list:
                    _datetime = self.parse_datetime(line[1:18], self.DT_FORMAT_CTA)
                    if _datetime:
                        min_datetime = _datetime
                for line in line_list[::-1]:
                    _datetime = self.parse_datetime(line[1:18], self.DT_FORMAT_CTA)
                    if _datetime:
                        max_datetime = _datetime
                # 日志命中时间区间
                if (min_datetime <= self.start_time and self.end_time <= max_datetime) \
                        or (self.start_time <= min_datetime <= self.end_time) \
                        or (self.start_time <= max_datetime <= self.end_time):
                    log.info("Log[{}] > get data from {}".format(self.service_code, file_name))
                    previous_key = None
                    for line in line_list:
                        log_datetime = self.parse_datetime(line[1:18], self.DT_FORMAT_CTA)
                        if not log_datetime:
                            if previous_key:
                                log_dict[previous_key].append(line)
                            continue
                        if log_datetime and self.start_time <= log_datetime <= self.end_time:
                            key = int(log_datetime.timestamp())
                            if key not in log_dict:
                                log_dict[key] = []
                            if not self.key_word or self.key_word in line:
                                log_dict[key].append(line)
                                previous_key = key
        # 排序分页
        data = []
        keys = sorted(log_dict)
        match_count = 0
        for key in keys:
            logs = log_dict.get(key)
            for _log in logs:
                if len(data) >= self.page_size:
                    return data
                if self.start_index <= match_count <= self.end_index:
                    data.append(_log)
                match_count += 1
        return data


class LogDownloadHandler:
    LOG_ROOT = "/code/data/log"

    def __init__(self, service_code):
        self.service_code = service_code

    def find_logs(self):
        service_log_dir = os.path.join(self.LOG_ROOT, self.service_code)
        file_list = [f for f in os.listdir(service_log_dir) if not f.startswith(".")]
        if self.service_code in ["ugs-cta", "ugs-ctp"]:
            log_dir = os.path.join(self.LOG_ROOT, "alg-{}".format(self.service_code[4:]))
            file_list.extend(os.listdir(log_dir))
        file_list.sort()
        return file_list

    def get_path(self, file_name):
        file_path = os.path.join(self.LOG_ROOT, self.service_code, file_name)
        if os.path.exists(file_path):
            return file_path
        if self.service_code in ["ugs-cta", "ugs-ctp"]:
            file_path = os.path.join(self.LOG_ROOT, "alg-{}".format(self.service_code[4:]), file_name)
            if os.path.exists(file_path):
                return file_path
        return None
