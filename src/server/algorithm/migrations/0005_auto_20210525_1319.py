# Generated by Django 2.0.5 on 2021-05-25 13:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('algorithm', '0004_auto_20210401_1411'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlgorithmCallBack',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('callback', models.BooleanField(default=True, verbose_name='是否回传')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_timestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('comment', models.CharField(blank=True, max_length=255, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '报告回传',
                'verbose_name_plural': '报告回传',
                'db_table': 'algorithm_callback',
                'ordering': ['-timestamp'],
            },
        ),
    ]
