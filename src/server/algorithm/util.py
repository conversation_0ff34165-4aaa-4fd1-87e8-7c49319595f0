import requests
from server import settings
import datetime
from server.algorithm.models import AlgorithmTask
import signal
import platform


def check_queue_status(algorithm_type):
    '''
        :return: (status, queues_status, message)
        status: 当前请求成功失败
        queues_status: 当前是否有排队
        message: 错误消息
    '''
    queue_str = settings.TYPE_QUEUE_DICT.get(algorithm_type, "")
    if not queue_str:
        return False, None, "queue name is not define"
    try:
        url = "http://{ip}:{port}/api/queues/%2f/{queue_str}".format(ip=settings.MQ_HOST,
                                                                     port=int(settings.MQ_WEB_PORT),
                                                                     queue_str=queue_str)
        response = requests.get(url=url, auth=(settings.MQ_USERNAME, settings.MQ_PASSWORD), timeout=1)
    except requests.exceptions.RequestException as e:
        print(e)
        return False, None, "rabbitmq server shutdowwn, please restart"
    except Exception as e:
        return False, None, "{}".format(e)
    try:
        data = response.json()
        # comumer服务挂了堆积总数
        if data["messages_ready"] > 0:
            return False, None, "consumer server shutdown, please restart"
        # 消费确认数
        if data["messages_unacknowledged"] > 0:
            return True, True, data["messages_unacknowledged"]
        elif data["messages_unacknowledged"] == 0:
            return True, False, data["messages_unacknowledged"]
        return False, None, "unkown error"
    except Exception as e:
        # print(response.content)
        return False, None, "data read error"
