from django.db import models

MAX_LENGTH = 255


class AlgorithmResult(models.Model):
    uuid = models.CharField(primary_key=True,
                            max_length=MAX_LENGTH)
    image_series = models.CharField(max_length=MAX_LENGTH,
                                    verbose_name='序列UUID')
    algorithm_type = models.CharField(max_length=MAX_LENGTH,
                                      verbose_name='算法类型')
    algorithm_result = models.TextField(verbose_name='返回结果',
                                        null=True, blank=True)
    mask_url = models.TextField(verbose_name='算法mask',
                                null=True, blank=True)
    index = models.IntegerField(verbose_name='序列帧数',
                                default=0)
    create_time = models.DateTimeField(verbose_name='创建时间',
                                       auto_now=True,
                                       null=True, blank=True)
    task = models.ForeignKey('AlgorithmTask',
                             on_delete=models.CASCADE,
                             verbose_name='所属算法任务',
                             null=True, blank=True)

    class Meta:
        db_table = 'algorithm_result'
        verbose_name = '算法结果表'
        verbose_name_plural = '算法结果表'
        ordering = ['index']

    def __str__(self):
        return self.image_series

    def to_dict(self):
        return dict(
            uuid=self.uuid,
            image_series=self.image_series,
            algorithm_type=self.algorithm_type,
            algorithm_result=self.algorithm_result,
            index=self.index,
            mask_url=self.mask_url
        )


class AlgorithmTask(models.Model):
    uuid = models.CharField(primary_key=True, max_length=MAX_LENGTH, verbose_name='uuid')
    series_uid = models.CharField(verbose_name='序列uid', max_length=MAX_LENGTH)
    user_id = models.CharField(verbose_name='进行算法任务人员', max_length=MAX_LENGTH)
    algorithm_type = models.CharField(verbose_name='算法类型', max_length=MAX_LENGTH)
    finish_percent = models.IntegerField(default=0, verbose_name='完成百分比')
    error_code = models.SmallIntegerField(verbose_name="错误码", default=0)
    finish_dated = models.DateTimeField(verbose_name='任务完成时间', auto_now=True)
    start_dated = models.DateTimeField(verbose_name='任务开始时间', auto_now_add=True)
    total_task = models.IntegerField(default=0, verbose_name='总体任务次数')
    finish_task = models.IntegerField(default=0, verbose_name='完成任务次数')

    class Meta:
        db_table = 'algorithm_task'
        verbose_name = '算法任务'
        verbose_name_plural = '算法任务'
        ordering = ['-start_dated']

    def __str__(self):
        return self.series_uid + '-' + self.user_id

    def to_dict(self):
        return dict(
            uuid=self.uuid,
            series_uid=self.series_uid,
            user_id=self.user_id,
            algorithm_type=self.algorithm_type,
            finish_percent=self.finish_percent,
            finish_dated=self.finish_dated.strftime('%Y-%m-%d %H:%M:%S'),
            start_dated=self.start_dated.strftime('%Y-%m-%d %H:%M:%S'),
            total_task=self.total_task,
            finish_task=self.finish_task
        )

