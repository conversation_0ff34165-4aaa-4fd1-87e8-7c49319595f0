import importlib
import json
import logging

from bson.objectid import ObjectId
from django.http import JsonResponse

from server.common.views import BaseView
from .models import (AlgorithmResult,
                     AlgorithmTask)
from ..study.models import Study

log = logging.getLogger("django")

async_models = importlib.import_module('server.async.models')


from django.db.models import Max, Min
from server.common.base import *
from server.common.mongoConnector import MongoDB
from server.common.utils import PACSUtil

Message = "算法任务"


def get_algorithm_textresult(study_uid, series_uids, res):
    """
    获取算法计算结果，    抽离代码复用
    """
    for series_uid in series_uids:
        task_queryset = AlgorithmTask.objects.filter(series_uid=series_uid)
        if not task_queryset.exists():
            continue
        task = task_queryset.first()
        algorithm_type = task.algorithm_type
        finish_percent = task.finish_percent
        error_code = task.error_code
        # error_message = Algorithm_ERROR.get(finish_percent, "")
        if finish_percent > 100:
            log.info("algorithm task failed, finishPercent:{}, errorCode:{}".format(finish_percent, error_code))
            error_message = settings.ERROR_CODE.get(error_code, "未能完成计算")
            res[algorithm_type] = dict(status=False, value=error_message,
                                       algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_FAILURE,
                                       error_code=task.error_code)
            continue
        # 对于cta， 目前没有result结果文本，那么对应的algorithm_result也没有，那么就使用finish_percent=100 erro_code=0 来判断状态为True
        if finish_percent == 100 and algorithm_type == "cta" and error_code == 0:
            res["cta"] = dict(status=True, value="", algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_SUCCESS)
            continue
        study = Study.objects.filter(study_instance_uid=study_uid).first()
        if study.toshiba and algorithm_type == "ctp":
            # 东芝灌注数据通过studyUID获取算法结果（Algorithm_result.image_series保存的是studyUID）
            algorithm_result_query = AlgorithmResult.objects.filter(image_series=study_uid).first()
        else:
            # 正常数据通过任务获取算法结果（Algorithm_result.image_series保存的是seriesUID）
            algorithm_result_query = AlgorithmResult.objects.filter(task=task).first()
        if algorithm_type == "ctp" and res["ctp"]["status"]:
            continue
        if not algorithm_result_query:
            res[algorithm_type] = dict(status=False, value="",
                                       algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_CALCULATING)
            continue
        log.info("study[{}] > algorithm_type:{}, series:{}".format(study_uid, algorithm_type, series_uid))
        mongo_id = algorithm_result_query.algorithm_result
        mongodb = MongoDB()
        results = mongodb.query(ObjectId(mongo_id), 'algorithm', '_id')
        if results:
            for result in results:
                algorithm_content = result.get('result', '')
                log.info("study[{}] > algorithm_type:{}, algorithm result:{}"
                         .format(study_uid, algorithm_type, algorithm_content))
                dict_check = json.loads(algorithm_content)
                if algorithm_type == 'aspects':
                    aspects_result = {}
                    if isinstance(dict_check, dict):
                        aspects_result["infarct_result"] = dict_check.get('infarct_result', None)
                        aspects_result["blood_location"] = dict_check.get("blood_location", None)
                        aspects_result["blood_volume"] = dict_check.get("blood_volume", None)
                        # 获取双侧评分
                        if "scoreLeftModify" in dict_check:
                            aspects_result["scoreLeft"] = dict_check.get("scoreLeftModify", "")
                            aspects_result["scoreRight"] = dict_check.get("scoreRightModify", "")
                        elif "scoreLeft" in dict_check:
                            aspects_result["scoreLeft"] = dict_check.get("scoreLeft", "")
                            aspects_result["scoreRight"] = dict_check.get("scoreRight", "")
                    else:
                        aspects_result["infarct_result"] = None
                        aspects_result["blood_location"] = None
                        aspects_result["blood_volume"] = None
                    if not aspects_result.get("blood_location", None):
                        del aspects_result["blood_location"]
                        del aspects_result["blood_volume"]
                    res["aspects"] = dict(status=True, value=aspects_result,
                                          algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_SUCCESS)
                elif algorithm_type == 'ctp':
                    ctp_result = {}
                    ctp_result["result_affectedSide"] = dict_check.get("affectedSide", "")
                    ctp_result["result_cbf"] = round(dict_check.get("v_cbf", '')[0], 2) if dict_check.get(
                        "v_cbf", '') else 0
                    # 低灌注体积Tmax>6s取Tmax第3值
                    ctp_result["result_TMax"] = round(dict_check.get("v_TMax", '')[2], 2) if dict_check.get(
                        "v_TMax", 0) else 0
                    # 低灌和核心梗死不匹配体积
                    ctp_result["result_TMaxcbf"] = round(ctp_result["result_TMax"] - ctp_result["result_cbf"], 2)
                    if ctp_result["result_TMax"] and ctp_result["result_cbf"]:
                        try:
                            ctp_result["result_ratio"] = round(ctp_result["result_TMax"] / ctp_result["result_cbf"],
                                                               2)
                        except Exception as e:
                            ctp_result["result_ratio"] = "Inf"
                    else:
                        ctp_result["result_ratio"] = "Inf"
                    res["ctp"] = dict(status=True, value=ctp_result,
                                      algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_SUCCESS)
                elif algorithm_type == 'cta':
                    cta_result = {}
                    try:
                        cta_result["cta_fraction"] = dict_check.get('cta_fraction')
                    except:
                        cta_result["cta_fraction"] = None
                    res["cta"] = dict(status=True, value=cta_result,
                                      algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_SUCCESS)
    return res


class AlgorithmTextResult(BaseView):
    """算法文本结果获取"""

    class Const:
        """算法结果常量枚举"""

        ALGORITHM_STATE_WAIT = 0  # 未计算
        ALGORITHM_STATE_CALCULATING = 1  # 计算中
        ALGORITHM_STATE_FAILURE = 2  # 计算失败
        ALGORITHM_STATE_SUCCESS = 3  # 计算成功

    def get(self, request):
        data = request.GET
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        study_uid = data.get('StudyUid')
        if not study_uid:
            response['message'] = '缺少StudyUid'
            return JsonResponse(response)
        init = PACSUtil()
        series_uids = init.get_study_series(study_id=study_uid)
        log.info("study[{}] > series_uids:{}".format(study_uid, series_uids))
        res = {
            'ctp': dict(status=False, value="", algorithm_state=self.Const.ALGORITHM_STATE_WAIT),
            'aspects': dict(status=False, value=""),
            'cta': dict(status=False, value="")
        }
        if not series_uids:
            return self.ok(res)
        res = get_algorithm_textresult(study_uid, series_uids, res)
        return self.ok(res)


class AlgorithmTime(BaseView):
    """算法计算耗时获取"""

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = {}
        ctp_info = async_models.RecordTime.objects.filter(category='ctp').aggregate(
            ctp_slow=Max('consume_time'), ctp_fast=Min('consume_time'))
        # 秒更新为分钟
        ctp_slow = ctp_info.get('ctp_slow', None)
        ctp_info['ctp_slow'] = round(ctp_slow / 60, 2) if ctp_slow else None
        ctp_fast = ctp_info.get('ctp_fast', None)
        ctp_info['ctp_fast'] = round(ctp_fast / 60, 2) if ctp_fast else None

        aspects_info = async_models.RecordTime.objects.filter(category='aspects').aggregate(
            aspects_slow=Max('consume_time'), aspects_fast=Min('consume_time'))
        aspects_slow = aspects_info.get('aspects_slow', None)
        aspects_info['aspects_slow'] = round(aspects_slow / 60, 2) if aspects_slow else None
        aspects_fast = aspects_info.get('aspects_fast', None)
        aspects_info['aspects_fast'] = round(aspects_fast / 60, 2) if aspects_fast else None

        data['ctp_info'] = ctp_info
        data['aspects_info'] = aspects_info
        response['data'] = data
        response["status"] = True
        response["code"] = 200
        return JsonResponse(response)

