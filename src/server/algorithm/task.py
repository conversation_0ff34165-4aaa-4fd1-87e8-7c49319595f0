from __future__ import absolute_import, unicode_literals

import copy
import json
import logging
import uuid

import pika
from celery import shared_task
from server import settings


logger = logging.getLogger("django")


def producer(queue, body):
    credentials = pika.PlainCredentials(settings.MQ_USERNAME,
                                        settings.MQ_PASSWORD)
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(settings.MQ_HOST, settings.MQ_PORT,
                                  credentials=credentials,
                                  blocked_connection_timeout=300)
    )
    channel = connection.channel()
    try:
        channel.queue_declare(queue=queue, durable=True)
    except:
        channel = connection.channel()
        channel.queue_delete(queue=queue)
        channel.queue_declare(queue=queue, durable=True)
    logger.info("send Q > queue:{}, bo")
    channel.basic_publish(exchange='',
                          routing_key=queue,
                          properties=pika.BasicProperties(
                              content_type="application/json",
                              delivery_mode=2
                          ),
                          body=json.dumps(body))
    connection.close()
    return True


# producer('hhh',body={"name":222})
# # for i in range(30):
# #     producer('hhh',body={"name":222})

@shared_task
def send(**option):
    """
    发送到信息到rabbitmq方法
    {
      "task_code": "935d5678-0ef1-11e8-b79a-bdd1b005bbb2",
      "arguments": [
        {
          "username": "root",
          "password": "Root@123",
          "port": 3306,
          "host": "************",
          "module": "mysql"
        }
      ],
      "type": "hour",
      "start_time": "2018-1-1 1:29:30",
      "ip": "************-100"
    }
    :return:
    """
    # response = dict(
    #     status=False,
    #     data=None,
    #     error=None
    # )
    # ip = option.get('ip', None)
    # task_id = option.get('task_id', None)
    arguments = option.get('SeriesInstanceUID', None)
    task_uuid = option.get('task_uuid', None)
    task_type = option.get('type', None)
    algorithm_type = option.get('algorithm_type', None)
    # task_type = option.get('type', None)
    producer('algorithm_send',
             dict(SeriesInstanceUID=arguments,
                  task_uuid=task_uuid,
                  type=task_type,
                  algorithm_type=algorithm_type)
             )


if __name__ == '__main__':
    import os
    import sys

    sys.path.append("D:\\code\\cloud-platform-backend")
    # sys.path.append("/code")
    #
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
    import django

    django.setup()
    from server.algorithm.models import *

    data_list = [
        dict(
            SeriesInstanceUID="********.1107.5.1.4.53621.30000014070423244203100005720",
            algorithm_type="aspects",
            queue="algorithm_send_aspect"
        ),
        dict(
            SeriesInstanceUID="********.1107.5.1.4.53621.30000017090323351764000002159",
            algorithm_type="aspects",
            queue="algorithm_send_aspect"
        ),
        dict(
            SeriesInstanceUID="1.3.46.670589.33.1.63663366750998029600002.5220143018370006140",
            algorithm_type="ctp",
            queue="algorithm_send_ctp"
        ),
        dict(
            SeriesInstanceUID="1.3.46.670589.33.1.63666207444743647800002.5732398364182397898",
            algorithm_type="ctp",
            queue="algorithm_send_ctp"
        ),
    ]
    for i in data_list:
        task_uuid = str(uuid.uuid1())
        AlgorithmTask.objects.create(uuid=task_uuid,
                                     series_uid=i["SeriesInstanceUID"],
                                     algorithm_type=i["algorithm_type"],
                                     user_id="1234567")
        data = dict(SeriesInstanceUID=i["SeriesInstanceUID"],
                    task_uuid=task_uuid,
                    algorithm_type=i["algorithm_type"]
                    )
        producer(i["queue"], body=data)
