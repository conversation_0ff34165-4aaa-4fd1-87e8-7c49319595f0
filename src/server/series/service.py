#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/19 10:53
"""
import json
import logging
import os.path
import shutil
import threading
import time
import traceback
from typing import Dict, List, Tuple
import uuid
from decimal import Decimal

import <PERSON>IT<PERSON> as sitk
import cv2
import numpy as np
import pydicom
from PIL import Image

from bson import ObjectId
from django import db
from django.db.models import F, Count
from pandas.io.json import json_normalize
from pydicom import uid
from shapely import Polygon, MultiPolygon

from server import settings
from server.algorithm.models import AlgorithmTask, AlgorithmResult
from server.image.models import CallBackDICOM
from server.common.code import RetCode, Const, Config
from server.common.mongoConnector import MongoDB
from server.common.remote_api import RabbitMQProducer, OrthancApi
from server.series.models import Series, SeriesSplit
from server.systemconfig.system_config_utils import SystemConfigUtils
from server.ge.ge_api import (
    ge_apply_calc_schedule_notice,
    validate_dicom_failed, storage_data
)
from server.user.auth import AuthVerify


log = logging.getLogger("django")

DCM_DIR = os.path.join(settings.DOCKER_DATA_BASE_DIR, "dcm")


class SeriesDownloader:
    def __init__(self, study_instance_uid, series_instance_uid, series_orthanc_id, is_toshiba_ctp):
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.series_orthanc_id = series_orthanc_id
        self.target_dir = os.path.join(DCM_DIR, study_instance_uid if is_toshiba_ctp else series_instance_uid)
        self.fail_counter = 0

    def run(self):
        """
        下载原图
        :return:
        """
        start = time.time()
        image_list = OrthancApi.query_image(self.series_orthanc_id)
        query_size = len(image_list)
        if settings.DUPLICATED_INSTANCES_FIX:
            data_frame = json_normalize(image_list)
            duplicated_instances = data_frame.duplicated(subset=["ParentSeries", "MainDicomTags.InstanceNumber"])
            deduplicated_list, deleted_list = [], []
            for index, value in duplicated_instances.items():
                if value:
                    deleted_list.append(data_frame.iloc[[index]][["MainDicomTags.SOPInstanceUID"]].values[0][0])
                    continue
                deduplicated_list.append(image_list[index])
            if deleted_list:
                CallBackDICOM.objects.filter(sop_instance_uid__in=deleted_list).delete()
                log.info("DICOM[study:{},series:{}] > delete {} images".format(
                    self.study_instance_uid, self.series_instance_uid, len(deleted_list)))
            image_list = deduplicated_list
        for image in image_list:
            file_uid = image.get("FileUuid")
            sop_instance_uid = image.get("MainDicomTags", {}).get("SOPInstanceUID")
            self.copy_dicom(file_uid, sop_instance_uid)
        log.info("DICOM[study:{},series:{}] > download {}/{} dicom in {:.2f}s".format(
            self.study_instance_uid, self.series_instance_uid, len(image_list), query_size, (time.time() - start)))
        return self.fail_counter == 0

    def copy_dicom(self, file_uid, sop_instance_uid):
        """
        拷贝原图
        :param file_uid: 文件标识
        :param sop_instance_uid: 实例标识
        :return:
        """
        if len(file_uid) <= 4:
            return
        file_path = os.path.join(settings.ORTHANC_STORAGE_PATH, file_uid[0:2], file_uid[2:4], file_uid)
        try:
            if not os.path.exists(self.target_dir):
                os.makedirs(self.target_dir, exist_ok=True)
            target_path = os.path.join(self.target_dir, "{}.dcm".format(sop_instance_uid))
            # 避免重复下载
            old_file = os.path.join(self.target_dir, file_uid)
            if os.path.exists(old_file):
                os.rename(old_file, target_path)
            if not os.path.exists(target_path):
                shutil.copyfile(file_path, target_path)
            path = target_path.replace(settings.DOCKER_DATA_BASE_DIR, "")
            CallBackDICOM.objects.filter(sop_instance_uid=sop_instance_uid).update(path=path)
        except:
            self.fail_counter += 1
            log.error("DCM[study:{},series:{}] > failed to download {}, {}".format(
                self.study_instance_uid, self.series_instance_uid, file_path, traceback.format_exc()))


class SeriesThumbnail:
    @staticmethod
    def generate_thumbnail(study_instance_uid, series_instance_uid, use_orthanc=False):
        """
        生成序列缩略图
        :param study_instance_uid: 检查标识
        :param series_instance_uid: 序列标识
        :param use_orthanc: 使用orthanc显示缩略图
        :return:
        """
        callback_dicom_queryset = CallBackDICOM.objects.filter(study_instance_uid=study_instance_uid,
                                                               series_instance_uid=series_instance_uid)
        image_count = callback_dicom_queryset.count()
        thumbnail_index = int(image_count / 2)
        if thumbnail_index == 0:
            thumbnail_index = 1
        callback_dicom_list = callback_dicom_queryset.all().order_by("instance_number")[
                              thumbnail_index - 1:thumbnail_index]
        if not callback_dicom_list.exists():
            return ""
        thumbnail_dicom = callback_dicom_list[0]
        log.info("Thumbnail[study:{}, series:{}] > image count:{}, thumbnail index:{}, instance number:{}".format(
            study_instance_uid, series_instance_uid, image_count, thumbnail_index, thumbnail_dicom.instance_number))
        if use_orthanc:
            return thumbnail_dicom.sop_orthanc_uuid
        thumbnail_dir = os.path.join(settings.DOCKER_DATA_BASE_DIR, "static", study_instance_uid, "THUMBNAIL")
        if not os.path.exists(thumbnail_dir):
            os.makedirs(thumbnail_dir)
        thumbnail_path = os.path.join(thumbnail_dir, "{}.png".format(thumbnail_index))
        if os.path.exists(thumbnail_path):
            os.remove(thumbnail_path)
        try:
            itk_img = sitk.ReadImage(os.path.join(settings.DOCKER_DATA_BASE_DIR, thumbnail_dicom.path[1:]))
            itk_img = sitk.IntensityWindowing(itk_img, 0, 100, 0, 255)
            img_array = sitk.GetArrayFromImage(itk_img)
            shape = img_array.shape
            img_array = np.reshape(img_array, (shape[1], shape[2]))
            high_window = np.max(img_array)
            low_window = np.min(img_array)
            lung_win = np.array([low_window * 1., high_window * 1.])
            new_img = (img_array - lung_win[0]) / (lung_win[1] - lung_win[0])
            scaled_image = np.uint8(new_img * 255)
            final_image = Image.fromarray(scaled_image)
            final_image.save(thumbnail_path)
            return thumbnail_path.replace(settings.DOCKER_DATA_BASE_DIR, "")
        except:
            log.error("Thumbnail[study:{}, series:{}] > gerenate error:{}".format(
                study_instance_uid, series_instance_uid, traceback.format_exc()))
            return ""


class SeriesSplitter:

    def __init__(self, series_instance_uid):
        self.series_instance_uid = series_instance_uid
        self.is_one_stop_scan = int(SystemConfigUtils.get_config(code=Config.ONE_STOP_SCAN_SWITCH, def_value="0"))
        self.is_cta_multi_phase = int(SystemConfigUtils.get_config(code=Config.CTA_MULTI_PHASE_SWITCH, def_value="0"))

    def split(self):
        """
        序列拆分入口
        :return:
        """
        split_series = []
        series = Series.objects.get(series_instance_uid=self.series_instance_uid)
        algorithm_type = series.type
        if algorithm_type == Const.ALGORITHM_TYPE_CTP and self.is_one_stop_scan:
            split_series.extend(self.__split_one_stop_series(series))
        if algorithm_type == Const.ALGORITHM_TYPE_CTA and self.is_cta_multi_phase:
            split_series.extend(self.__split_multi_phase_series(series))
        return split_series

    def __split_one_stop_series(self, original_series):
        """
        一站式扫描序列拆分
        :param original_series: 原始序列
        :return: 拆分后的序列信息
        """

        study_instance_uid = original_series.study.study_instance_uid
        new_series = []
        slice_thickness_list = CallBackDICOM.objects.filter(
            study_instance_uid=study_instance_uid, series_instance_uid=original_series.series_instance_uid).values(
            "slice_thickness").annotate(number=Count("uuid")).order_by("-number")
        slice_thickness_list = list(slice_thickness_list)
        log.info("thickness group:{}".format(slice_thickness_list))
        if len(slice_thickness_list) == 1:
            return new_series
        ctp_slice_thickness = float(slice_thickness_list[0]["slice_thickness"])
        phase_list = self.__get_phase(original_series)
        series_number = int(original_series.series_number)
        for phase_item in phase_list:
            phase_slice_thickness = phase_item["sliceThickness"]
            if phase_slice_thickness == ctp_slice_thickness:
                continue
            series_instance_uid = uid.generate_uid()
            series_description = "uCTA"
            series_number += 1
            # 创建拆分序列
            series = Series.objects.create(id=str(uuid.uuid1()),
                                           series_instance_uid=series_instance_uid,
                                           modality=original_series.modality,
                                           series_description=series_description,
                                           type=Const.ALGORITHM_TYPE_CTA,
                                           series_number=series_number,
                                           series_date=original_series.series_date,
                                           series_time=original_series.series_time,
                                           body_part_examined=original_series.body_part_examined,
                                           slice_thickness=phase_slice_thickness,
                                           study=original_series.study)
            series_dir = os.path.join(DCM_DIR, series_instance_uid)
            if not os.path.exists(series_dir):
                os.makedirs(series_dir)
            phase_image_list = phase_item["images"]
            for sop_instance_uid in phase_image_list:
                image_path = os.path.join(DCM_DIR, original_series.series_instance_uid, "{}.dcm".format(sop_instance_uid))
                dataset = pydicom.read_file(image_path)
                dataset.SeriesInstanceUID = series_instance_uid
                dataset.SeriesDescription = series_description
                dataset.SeriesNumber = series_number
                new_path = image_path.replace(original_series.series_instance_uid, series_instance_uid)
                dataset.save_as(new_path)
                os.remove(image_path)
                # 更新图像信息
                CallBackDICOM.objects.filter(sop_instance_uid=sop_instance_uid).update(
                    series_instance_uid=series_instance_uid,
                    path=new_path.replace(settings.DOCKER_DATA_BASE_DIR, ""))
            # 创建序列拆分记录
            SeriesSplit.objects.create(id=str(uuid.uuid1()),
                                       series_instance_uid=series_instance_uid,
                                       original_series=original_series.series_instance_uid,
                                       image_number=len(phase_image_list),
                                       type=1)
            new_series.append(series)
        return new_series

    def __split_multi_phase_series(self, original_series):
        """
        CTA多期相序列拆分
        :param original_series: 原始序列
        :return: 拆分后的序列信息
        """
        new_series = []
        phase_list = self.__get_phase(original_series)
        series_number = int(original_series.series_number)
        for index, phase_item in enumerate(phase_list):
            if index == 0:
                continue
            series_instance_uid = uid.generate_uid()
            series_number += 1
            # 创��拆分序列
            series = Series.objects.create(id=str(uuid.uuid1()),
                                           series_instance_uid=series_instance_uid,
                                           modality=original_series.modality,
                                           series_description=original_series.series_description,
                                           type=Const.ALGORITHM_TYPE_CTA,
                                           series_number=series_number,
                                           series_date=original_series.series_date,
                                           series_time=original_series.series_time,
                                           body_part_examined=original_series.body_part_examined,
                                           slice_thickness=original_series.slice_thickness,
                                           study=original_series.study)
            series_dir = os.path.join(DCM_DIR, series_instance_uid)
            if not os.path.exists(series_dir):
                os.makedirs(series_dir)
            phase_image_list = phase_item["images"]
            for sop_instance_uid in phase_image_list:
                image_path = os.path.join(DCM_DIR, original_series.series_instance_uid, "{}.dcm".format(sop_instance_uid))
                dataset = pydicom.read_file(image_path)
                dataset.SeriesInstanceUID = series_instance_uid
                dataset.SeriesNumber = series_number
                new_path = image_path.replace(original_series.series_instance_uid, series_instance_uid)
                dataset.save_as(new_path)
                os.remove(image_path)
                # 更新图像信息
                CallBackDICOM.objects.filter(sop_instance_uid=sop_instance_uid).update(
                    series_instance_uid=series_instance_uid, path=new_path.replace(settings.DOCKER_DATA_BASE_DIR, ""))
            # 创建序列拆分记录
            SeriesSplit.objects.create(id=str(uuid.uuid1()),
                                       series_instance_uid=series_instance_uid,
                                       original_series=original_series.series_instance_uid,
                                       image_number=len(phase_image_list),
                                       type=2)
            new_series.append(series)
        return new_series

    @staticmethod
    def __get_phase(series):
        """
        获取期相信息

        :param series: 原始序列
        :return: 期相分组信息
        """
        study_instance_uid = series.study.study_instance_uid
        image_list = CallBackDICOM.objects.filter(
            study_instance_uid=series.study.study_instance_uid, series_instance_uid=series.series_instance_uid).values(
            "uuid", "sop_instance_uid", "slice_thickness", "slice_position").order_by("instance_number")
        size = len(image_list)
        log.info("Splitter[study:{}, series:{}] > images size:{}".format(
            study_instance_uid, series.series_instance_uid, len(image_list)))
        phase_list = []
        group = []
        previous_slice_position = None
        spacing_between_slices = None
        for index, image in enumerate(image_list):
            slice_position = Decimal(image["slice_position"])
            if index == 0:
                previous_slice_position = slice_position
                group.append(image["sop_instance_uid"])
                continue
            if not spacing_between_slices:
                spacing_between_slices = slice_position-previous_slice_position
            if previous_slice_position + spacing_between_slices == slice_position:
                group.append(image["sop_instance_uid"])
                previous_slice_position = slice_position
                if index == (size - 1):
                    phase_list.append(dict(sliceThickness=float(image["slice_thickness"]), images=group))
                continue
            previous_slice_position = slice_position
            previous_image = image_list[index - 1]
            phase_list.append(dict(sliceThickness=float(previous_image["slice_thickness"]), images=group.copy()))
            group.clear()
            spacing_between_slices = None
            group.append(image["sop_instance_uid"])
        log.info("Splitter[study:{}, series:{}] > phase size:{}".format(
            study_instance_uid, series.series_instance_uid, len(phase_list)))
        return phase_list


class SeriesPushHandler:

    def __init__(self, series_id, series_orthanc_id, ge_callback):
        series = Series.objects.get(id=series_id)
        study = series.study
        self.hospital_uid = series.hospital_uid
        self.study_instance_uid = study.study_instance_uid
        self.toshiba = study.toshiba
        self.original_series = series
        self.original_series_orthanc_id = series_orthanc_id
        self.ge_callback = ge_callback

    def start(self):
        """
        序列推送入口
        :return:
        """
        algorithm_type = self.original_series.type
        original_series_instance_uid = self.original_series.series_instance_uid
        if algorithm_type == Const.ALGORITHM_TYPE_OTHER:
            thumbnail = SeriesThumbnail.generate_thumbnail(self.study_instance_uid, original_series_instance_uid, True)
            self.original_series.thumbnail = thumbnail
            self.original_series.downloaded = True
            self.original_series.save()
            return
        is_toshiba_ctp = (self.toshiba and algorithm_type == Const.ALGORITHM_TYPE_CTP)
        if is_toshiba_ctp:
            log.info("Series[callback] > study:{}, series:{}, toshiba ctp series, ignore push".format(
                self.study_instance_uid, self.original_series_orthanc_id))
            return
        downloader = SeriesDownloader(self.study_instance_uid, original_series_instance_uid,
                                      self.original_series_orthanc_id, False)
        if not downloader.run():
            log.info("Series[callback] > study:{}, series:{}, failed to download dicom".format(
                self.study_instance_uid, original_series_instance_uid))
            return
        if is_toshiba_ctp:
            log.info("Series[callback] > study:{}, series:{}, toshiba ctp series, ignore push".format(
                self.study_instance_uid, self.original_series_orthanc_id))
            thumbnail_path = SeriesThumbnail.generate_thumbnail(self.study_instance_uid, original_series_instance_uid)
            self.original_series.thumbnail_path = thumbnail_path
            self.original_series.downloaded = True
            self.original_series.save()
            return

        series_split = [self.original_series]
        splitter = SeriesSplitter(original_series_instance_uid)
        new_series = splitter.split()
        log.info("Series[callback] > study:{}, series:{}, split {} series".format(
            self.study_instance_uid, original_series_instance_uid, len(new_series)))
        series_split.extend(new_series)
        for series in series_split:
            thumbnail_path = SeriesThumbnail.generate_thumbnail(self.study_instance_uid, series.series_instance_uid)
            series.thumbnail_path = thumbnail_path
            series.downloaded = True
            series.save()
            if self.ge_callback:
                series_uid = series.series_instance_uid
                is_split_series = (series_uid != original_series_instance_uid)
                self.call_ge(series_uid, is_split_series)
                continue
            self.calculate_series(series)

    def call_ge(self, series_instance_uid, is_split_series):
        """
        向魔盒申请调度资源
        :param series_instance_uid: 序列标识
        :param is_split_series: 是否为拆分序列
        :return:
        """
        calc_no = f"{self.study_instance_uid},{series_instance_uid}"
        resource_type = "series"
        resource_id = series_instance_uid
        if is_split_series:
            resource_type = "series_split"
            resource_id = self.original_series.series_instance_uid
        ret_code = ge_apply_calc_schedule_notice(calc_no, resource_type, resource_id)
        log.info("GE[apply] > series:{}, calcNo:{}, resourceId:{}, resourceType:{}, result:{}".format(
            series_instance_uid, calc_no, resource_id, resource_type, ret_code))
        return

    @staticmethod
    def calculate_series(series, resource_data=None):
        """
        计算序列
        :param series: 序列信息
        :param resource_data: GE授予计算相关信息
        {"resourceId": "GPU ID  不大于7；[0-7]","resourceType": "GPU 类型","resourceCapacity": "GPU 容量",
        "resourceNode": "GPU所在位置","resourceMetadata": "GPU 元数据"}）
        :return:
        """
        study_instance_uid = series.study.study_instance_uid
        series_instance_uid = series.series_instance_uid
        algorithm_type = series.type
        # 创建算法任务
        algorithm_task = AlgorithmTask.objects.create(uuid=str(uuid.uuid1()), series_uid=series_instance_uid,
                                                      algorithm_type=algorithm_type, user_id="admin")
        queue_name = settings.TYPE_QUEUE_DICT.get(algorithm_type, None)
        message = dict(studyInstanceUID=study_instance_uid, seriesInstanceUID=series_instance_uid,
                       algorithmType=algorithm_type, resourceData=resource_data if resource_data else dict())
        # 是否回传
        callback = SystemConfigUtils().getConfigValue(code="canReportBack", def_value="")
        message["callback"] = eval(callback) if callback else True
        # 串并行
        message["taskType"] = SystemConfigUtils().getConfigValue(code="algorithmProcessMode", def_value="1")
        try:
            RabbitMQProducer.simple_send(queue_name=queue_name, message=message)
            return RetCode.OK
        except Exception:
            log.error(f"Series[callback] send message error: {traceback.format_exc()}")
            algorithm_task.delete()
            if resource_data:
                # 同步GE计算结果
                storage_data(calcNo=F"{study_instance_uid},{series_instance_uid}", aiModel=algorithm_type,
                             aiResultMsg={RetCode.SERIES_PUSH_ERROR.code, RetCode.SERIES_PUSH_ERROR.msg})
            return RetCode.SERIES_PUSH_ERROR

class SeriesCallbackService:

    def __init__(self, series_orthanc_id, tags, hospital_uid, hospital_name):
        self.series_orthanc_id = series_orthanc_id
        self.tags = tags
        self.hospital_uid = hospital_uid
        self.hospital_name = hospital_name
        self.study_instance_uid = None
        self.series_instance_uid = self.tags.get("SeriesInstanceUID")
        self.resource_uid = F"{self.study_instance_uid},{self.series_instance_uid}"
        ge_callback = int(SystemConfigUtils.get_config(code=Config.GE_CALLBACK_SWITCH, def_value="0"))
        self.ge_callback = True if ge_callback else False

    def do_post(self):
        """
        序列回调请求体处理入口
        :return:
        """
        series = Series.objects.filter(hospital_uid=self.hospital_uid,
                                       series_instance_uid=self.series_instance_uid).first()
        if not series:
            log.error("Series[callback] > series[{}] not found".format(self.series_instance_uid))
            return RetCode.SERIES_NOT_FOUND
        self.study_instance_uid = series.study.study_instance_uid
        self.check(series)
        threading.Thread(target=self.__async,
                         args=(series.id, self.series_orthanc_id, self.ge_callback)).start()
        return RetCode.OK

    def check(self, series):
        """
        检测算法
        :param series:
        :return:
        """
        series.orthanc_id = self.series_orthanc_id
        series.save()
        algorithm_type = series.type
        # ASPECTS 层厚检验
        if len(algorithm_type) == 0 or algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
            try:
                slice_thickness = float(series.slice_thickness)
                aspect_slice_thickness_min = int(SystemConfigUtils().getConfigValue(Config.ASPECT_SLICE_THICKNESS_MIN, 2))
                aspect_slice_thickness_max = int(SystemConfigUtils().getConfigValue(Config.ASPECT_SLICE_THICKNESS_MAX, 6))
                if not aspect_slice_thickness_min < slice_thickness <= aspect_slice_thickness_max:
                    log.info("Series[callback] > study:{}, series:{}, image slice thickness out of range".format(
                        self.study_instance_uid, self.series_instance_uid))
                    series.type = Const.ALGORITHM_TYPE_OTHER
                    series.save()
                    if self.ge_callback:
                        # 通知魔盒异常信息
                        validate_dicom_failed(message=RetCode.SERIES_IMAGE_SLICE_THICKNESS_OUT_OF_RANGE.msg,
                                              resource_type="series", ai_model=algorithm_type,
                                              resource_uid=self.resource_uid)
                    return
                if len(algorithm_type) == 0:
                    series.type = Const.ALGORITHM_TYPE_ASPECTS
                    series.save()
            except:
                log.error("Series[callback] > study:{}, series:{}, invalid slice thickness:{}".format(
                    self.study_instance_uid, self.series_instance_uid, series.slice_thickness))
                series.type = Const.ALGORITHM_TYPE_OTHER
                series.save()
                if self.ge_callback:
                    # 增加魔盒接口来通知魔盒异常信息
                    validate_dicom_failed(message=RetCode.SERIES_INVALID_SLICE_THICKNESS.msg,
                                          resource_type="series", ai_model=algorithm_type,
                                          resource_uid=self.resource_uid)
                return
        # CTP 图像数量检验
        if not series.study.toshiba and algorithm_type == Const.ALGORITHM_TYPE_CTP:
            image_count_number = CallBackDICOM.objects.filter(study_instance_uid=self.study_instance_uid,
                                                              series_instance_uid=self.series_instance_uid).count()
            min_ctp_image_num = SystemConfigUtils().getConfigValue(Config.CTP_IMAGE_NUMBER_MIN, 200)
            log.info("Series[callback] > study:{}, series:{}, ctp min number:{}, image number:{}".format(
                self.study_instance_uid, self.series_instance_uid, min_ctp_image_num, image_count_number))
            if image_count_number < int(min_ctp_image_num):
                log.info("Series[callback] > study:{}, series:{}, insufficient number of images".format(
                    self.study_instance_uid, self.series_instance_uid))
                series.type = Const.ALGORITHM_TYPE_OTHER
                series.save()
                if self.ge_callback:
                    # 增加魔盒接口来通知魔盒异常信息
                    validate_dicom_failed(message=RetCode.SERIES_CTP_IMAGES_ARE_NOT_ENOUGH.msg,
                                          resource_type="series", ai_model=algorithm_type,
                                          resource_uid=self.resource_uid)
                return
        # 模块授权校验
        if not AuthVerify.check_module(series.type):
            log.info("Series[callback] > study:{}, series:{}, failed to check module auth")
            series.type = Const.ALGORITHM_TYPE_OTHER
            series.save()
            if self.ge_callback:
                # 增加魔盒接口来通知魔盒异常信息
                validate_dicom_failed(message=RetCode.UNAUTHORIZED.msg, resource_type="series",
                                      resource_uid=self.resource_uid, ai_model=series.type)

    @staticmethod
    def __async(series_id, orthanc_id, ge_callback):
        """
        异步处理入口
        :param series_id: 序列ID
        :param orthanc_id: 序列Orthanc标识
        :param ge_callback: GE接入标识
        :return:
        """
        db.close_old_connections()
        handler = SeriesPushHandler(series_id, orthanc_id, ge_callback)
        handler.start()


class SeriesImageService:

    @staticmethod
    def get_image(series_instance_uid):
        """
        获取序列下的图像信息
        2023-04-20 ctp 结果序列增加imagePath字段

        :param series_instance_uid: 序列UID
        :return:
        """
        series = Series.objects.filter(series_instance_uid=series_instance_uid).first()
        if not series:
            log.error("Series[image] > series not found: {}".format(series_instance_uid))
            return RetCode.SERIES_NOT_FOUND, None
        study_instance_uid = series.study.study_instance_uid
        images = CallBackDICOM.objects.filter(
            study_instance_uid=study_instance_uid,
            series_instance_uid=series_instance_uid
        ).annotate(
            sopInstanceUID=F("sop_instance_uid"),
            instanceNumber=F("instance_number"),
            orthancId=F("sop_orthanc_uuid")
        ).values("sopInstanceUID", "instanceNumber", "orthancId", "path").order_by("instanceNumber")
        data = []
        if series.series_description == "USC-UGuard CTP Summary":
            # CTP结果序列，返回图像地址
            for image in images:
                path = image.get("path")
                if not path or "/report/USC_UGuard_CTP_Summary/" not in path:
                    data.append(image)
                    continue
                image_path = path.replace("/report/", "/png/")
                image_path_list = image_path.split("/")
                if not image_path_list:
                    continue
                dcm_name = image_path_list[-1]
                path_number = ""
                if "US" in dcm_name:
                    path_number = dcm_name.replace("US", "")
                if dcm_name.endswith(".dcm"):
                    path_number = dcm_name.replace(".dcm", "")
                    try:
                        path_number = "{}".format(int(path_number)-1)
                    except:
                        log.error("failed to convert {}, error:{}".format(path_number, traceback.format_exc()))
                if not path_number:
                    continue
                image_name = f"img_{path_number}.png"
                image_path_list[-1] = image_name
                png_path = "/".join(image_path_list)
                image["imagePath"] = png_path if os.path.exists(f"{settings.DOCKER_DATA_BASE_DIR}{png_path}") else ""
                data.append(image)
        elif series.type == Const.ALGORITHM_RESULT_TYPE_ASPECTS:
            # ASPECTS结果序列，返回图像地址
            for image in images:
                path = image.get("path")
                if not path:
                    data.append(image)
                    continue
                image_path = path.replace("/dcm/", "/pic/").replace(".dcm", ".jpg")
                if os.path.exists(os.path.join(settings.DOCKER_DATA_BASE_DIR, image_path[1:])):
                    image["imagePath"] = image_path
                data.append(image)
        else:
            data = list(images)
        log.debug("Series[image] > series[{}], find {} images".format(series_instance_uid, len(data)))
        return RetCode.OK, data


class SeriesResultService:

    @staticmethod
    def get_result(series_instance_uid):
        """
        获取算法结果

        :param series_instance_uid: 序列UID
        :return:
        """
        series = Series.objects.filter(series_instance_uid=series_instance_uid).first()
        if not series:
            log.info("Series[result] > series not found: {}".format(series_instance_uid))
            return RetCode.SERIES_NOT_FOUND, None
        study = series.study
        study_instance_uid = study.study_instance_uid
        algorithm_type = series.type
        ctp_report_merge = int(SystemConfigUtils().getConfigValue("ctpReportMerge", "2"))
        if algorithm_type == Const.ALGORITHM_TYPE_CTA or algorithm_type == Const.ALGORITHM_TYPE_ASPECTS or (
                algorithm_type == Const.ALGORITHM_TYPE_CTP and (study.toshiba or ctp_report_merge != 1)) or algorithm_type == Const.ALGORITHM_TYPE_OTHER:
            data = SeriesResultService.__get_response_body(study, series_instance_uid, algorithm_type)
            return RetCode.OK, data
        if algorithm_type == Const.ALGORITHM_TYPE_CTP and ctp_report_merge == 1:
            series_queryset = Series.objects.filter(study__id=study.id,
                                                    type=algorithm_type).order_by("gmt_modified").all()
            if len(series_queryset) <= 1:
                data = SeriesResultService.__get_response_body(study, series_instance_uid, algorithm_type)
                return RetCode.OK, data
            # 合并两段结果
            state_one, error_one, result_one = SeriesResultService.__get_task_result(
                study, series_queryset[0].series_instance_uid)
            state_two, error_two, result_two = SeriesResultService.__get_task_result(
                study, series_queryset[1].series_instance_uid)
            if error_one or error_two:
                _state = state_one if error_one else state_two
                _error = error_one if error_one else error_two
                return RetCode.OK, dict(state=_state, error=_error)
            if result_one and result_two:
                response_data = dict(state=state_one)
                total_tmax = []
                total_cbf = []
                for index in range(len(result_one["v_TMax"])):
                    total_tmax.append(result_one["v_TMax"][index] + result_two["v_TMax"][index])
                for index in range(len(result_one["v_cbf"])):
                    total_cbf.append(result_one["v_cbf"][index] + result_two["v_cbf"][index])
                result = dict(v_TMax=total_tmax, v_cbf=total_cbf)
                response_data[algorithm_type] = SeriesResultService.__parse_result(algorithm_type, result)
                return RetCode.OK, response_data
            else:
                data = SeriesResultService.__get_response_body(study, series_instance_uid, algorithm_type)
                return RetCode.OK, data
        return RetCode.OK, {"data": {"state": None}}

    @staticmethod
    def __get_response_body(study, series_instance_uid, algorithm_type):
        study_instance_uid = study.study_instance_uid
        state, error, result = SeriesResultService.__get_task_result(study, series_instance_uid)
        response_data = dict(state=state)
        if error:
            response_data["error"] = error
        if result:
            response_data[algorithm_type] = SeriesResultService.__parse_result(algorithm_type, result)
        return response_data

    @staticmethod
    def __get_task_result(study, series_instance_uid):
        """
        获取任务结果
        :param study: 检查
        :param series_instance_uid: 序列标识
        :return:
        """
        study_instance_uid = study.study_instance_uid
        algorithm_task = AlgorithmTask.objects.filter(series_uid=series_instance_uid).first()
        if not algorithm_task:
            log.info("study[{}] > series:{}, task not found".format(study_instance_uid, series_instance_uid))
            return None, None, None
        algorithm_type = algorithm_task.algorithm_type
        finish_percent = algorithm_task.finish_percent
        # 未完成
        if 0 <= finish_percent < 100:
            log.info("algorithm task processing, finishPercent:{}".format(finish_percent))
            state = Const.ALGORITHM_STATE_WAIT if finish_percent == 0 else Const.ALGORITHM_STATE_CALCULATING
            return state, None, None
        # 算法失败
        if finish_percent > 100:
            error_code = algorithm_task.error_code
            error_message = settings.ERROR_CODE.get(error_code, "")
            log.info("study[{}] > series:{}, algorithm:{}, errorCode:{}({})".format(
                study_instance_uid, series_instance_uid, algorithm_type, error_code, error_message))
            return Const.ALGORITHM_STATE_FAILURE, dict(msg=error_message, code=error_code), None
        # if algorithm_type == Const.ALGORITHM_TYPE_CTA:
        #     return Const.ALGORITHM_STATE_SUCCESS, None, None
        is_toshiba_ctp = algorithm_type == Const.ALGORITHM_TYPE_CTP and study.toshiba
        result_search = dict(image_series=study_instance_uid) if is_toshiba_ctp else dict(task=algorithm_task)
        algorithm_result = AlgorithmResult.objects.filter(**result_search).first()
        if not algorithm_result:
            log.info("study[{}] > {}: algorithm result not found".format(study_instance_uid, algorithm_type))
            return Const.ALGORITHM_STATE_CALCULATING, None, None
        # 获取算法结果
        algorithm_id = algorithm_result.algorithm_result
        mongodb = MongoDB()
        result_query = mongodb.get("algorithm", algorithm_id)
        if not result_query:
            log.info("mongodb result not found")
            return Const.ALGORITHM_STATE_SUCCESS, None, None
        text_content = result_query.get("result", "{}")
        algorithm_result = None
        try:
            algorithm_result = json.loads(text_content)
        except:
            log.info("mongodb result not found")
        if algorithm_result:
            return Const.ALGORITHM_STATE_SUCCESS, None, algorithm_result
        else:
            return Const.ALGORITHM_STATE_FAILURE, None, None

    @staticmethod
    def __parse_result(algorithm_type, text_dict):
        if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
            # 获取单侧评分
            infarct_result = text_dict.get("infarct_result", "")
            if infarct_result:
                return dict(infarctResult=infarct_result)
            # 获取双侧评分
            score_left = text_dict.get("scoreLeft", "")
            score_right = text_dict.get("scoreRight", "")
            if "scoreLeftModify" in text_dict:
                score_left = text_dict.get("scoreLeftModify", "")
                score_right = text_dict.get("scoreRightModify", "")
            if score_left or score_right:
                return dict(scoreLeft=score_left, scoreRight=score_right, ichVolume=text_dict.get("ichVolume", ""))
            # 获取新版结��
            return dict(frontCycleScoreLeft=text_dict.get("frontCycleScoreLeft", ""),
                        frontCycleScoreRight=text_dict.get("frontCycleScoreRight", ""),
                        postCycleScore=text_dict.get("postCycleScore", ""),
                        ichVolume=text_dict.get("ichVolume", ""))
        if algorithm_type == Const.ALGORITHM_TYPE_CTP:
            # 低灌注体积（Tmax>6.0s的区域）
            tmax = round(text_dict.get("v_TMax", "")[2], 2) if text_dict.get("v_TMax", 0) else 0
            # 核心梗死体积（rCBF<30%的区域）
            rcbf = round(text_dict.get("v_cbf", "")[0], 2) if text_dict.get("v_cbf", "") else 0
            # 低灌和核心梗死不匹配体积
            mismatch = round(tmax - rcbf, 2)
            log.info("ctp text result, tmax:{}, cbf:{}, mismatch:{}".format(tmax, rcbf, mismatch))
            mismatch_ratio = "Inf"
            if tmax and rcbf:
                try:
                    mismatch_ratio = round(tmax / rcbf, 2)
                except Exception:
                    log.error(traceback.format_exc())
            return dict(tmax=tmax, rcbf=rcbf, mismatch=mismatch, mismatchRatio=mismatch_ratio)
        return text_dict


    @staticmethod
    def update_result(series_instance_uid, body):
        """
        更新算法结果

        :param series_instance_uid: 序列UID
        :param body: 请求体
        :return:
        """
        series = Series.objects.filter(series_instance_uid=series_instance_uid).first()
        if not series:
            log.info("Series[result] > series not found: {}".format(series_instance_uid))
            return RetCode.SERIES_NOT_FOUND
        algorithm_type = body.get("algorithmType", "")
        if not algorithm_type:
            log.info("Series[result] > algorithmType is empty")
            return RetCode.SERIES_ALGORITHM_TYPE_IS_EMPTY
        if algorithm_type != series.type:
            log.info("Series[result] > invalid algorithmType")
            return RetCode.SERIES_INVALID_ALGORITHM_TYPE
        algorithm_result = AlgorithmResult.objects.filter(image_series=series_instance_uid).first()
        if not algorithm_result:
            log.info("Series[result] > algorithm result not found")
            return RetCode.SERIES_ALGORITHM_RESULT_NOT_FOUND
        result_id = algorithm_result.algorithm_result
        log.info("Series[result] > series:{}, algorithm:{}, result: {}".format(
            series_instance_uid, algorithm_type, result_id))
        if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
            front_cycle_score_left = body.get("frontCycleScoreLeft")
            front_cycle_score_right = body.get("frontCycleScoreRight")
            post_cycle_score = body.get("postCycleScore")
            if front_cycle_score_left is not None and front_cycle_score_right is not None and post_cycle_score is not None:
                if not isinstance(front_cycle_score_left, int) or not isinstance(front_cycle_score_right, int) \
                        or not isinstance(post_cycle_score, int):
                    log.info("Series[result] > score is invalid:{}".format(body))
                    return RetCode.SERIES_INVALID_ASPECTS_SCORE
                if not 0 <= front_cycle_score_left <= 10 or not 0 <= front_cycle_score_right <= 10 \
                        or not 0 <= post_cycle_score <= 10:
                    log.info("Series[result] > invalid score:{}".format(body))
                    return RetCode.SERIES_INVALID_ASPECTS_SCORE
                update_info = dict(frontCycleScoreLeft=front_cycle_score_left,
                                   frontCycleScoreRight=front_cycle_score_right, postCycleScore=post_cycle_score)
                SeriesResultService.__update_aspects(result_id, update_info)
                return RetCode.OK
            score_left = body.get("scoreLeft")
            score_right = body.get("scoreRight")
            if not isinstance(score_left, int) or not isinstance(score_right, int):
                log.info("Series[result] > scoreLeft or scoreRight is invalid")
                return RetCode.SERIES_INVALID_ASPECTS_SCORE
            if not 0 <= score_left <= 10 or not 0 <= score_right <= 10:
                log.info("Series[result] > left:{}, right:{}, invalid score".format(score_left, score_right))
                return RetCode.SERIES_INVALID_ASPECTS_SCORE
            update_info = {"scoreLeftModify": score_left, "scoreRightModify": score_right}
            SeriesResultService.__update_aspects(result_id, update_info)
        return RetCode.OK

    @staticmethod
    def __update_aspects(result_id, update_dict):
        """
        更新ASPECTS算法结果

        :param result_id: 结果ID
        :param update_dict: 更新信息
        :return:
        """
        log.info("Series[result] > update aspects result, id:{}, info:{}".format(result_id, update_dict))
        mongodb = MongoDB()
        result_list = mongodb.query(ObjectId(result_id), "algorithm", "_id")
        if not result_list:
            log.info("Series[result] > algorithm result not found:{}".format(result_id))
            return
        aspects_result = result_list[0]
        algorithm_content = aspects_result.get("result")
        result_dict = json.loads(algorithm_content)
        result_dict.update(update_dict)
        body = dict(result=json.dumps(result_dict))
        mongodb.update(body, "algorithm", {"_id": ObjectId(result_id)})
        log.info("Series[result] > aspects result[{}] updated".format(result_id))


class AlgoResultService:

    @staticmethod
    def get_cpr(series_instance_uid):
        """
        获取CPR文件
        :param series_instance_uid:
        :return:
        """
        series = Series.objects.filter(series_instance_uid=series_instance_uid).first()
        if not series:
            log.info("Series[result] > series not found: {}".format(series_instance_uid))
            return RetCode.SERIES_NOT_FOUND, None
        study_instance_uid = series.study.study_instance_uid
        cpr_dir = os.path.join("/code/server/static/cpr", study_instance_uid, series_instance_uid)
        link_prefix = F"/server/static/cpr/{study_instance_uid}/{series_instance_uid}"
        filename_list = os.listdir(cpr_dir)
        if not filename_list or len(filename_list) == 0:
            return RetCode.SERIES_CPR_NOT_FOUND, None
        data = []
        index_dict = {}
        for filename in filename_list:
            filename_list = filename.split(".")
            name = filename_list[0]
            suffix = filename_list[1]
            if name not in index_dict:
                data.append({"name": name})
                index_dict[name] = len(data) - 1
            url_key = "vtiUrl" if suffix == "vti" else "txtUrl"
            data[index_dict[name]][url_key] = F"{link_prefix}/{filename}"
        return RetCode.OK, data

    @staticmethod
    def get_tdc(series, idx_array):
        """
        获取TDC曲线
        :param series: 序列
        :param idx_array: 选点
        :return:
        """
        study = series.study
        tdc_dir = os.path.join(settings.DOCKER_DATA_BASE_DIR, "ctp", study.study_instance_uid)
        if not study.toshiba:
            tdc_dir = os.path.join(tdc_dir, series.series_instance_uid)
        tdc_file = os.path.join(tdc_dir, "tdc.nii.gz")
        json_file = os.path.join(tdc_dir, "tdc.json")
        if not os.path.exists(tdc_file) or not os.path.exists(json_file):
            return RetCode.SERIES_TDC_NOT_FOUND, []
        with open(json_file, "r", encoding="utf8") as f:
            tdc_info = json.load(f)
        time_interval = Decimal(tdc_info.get("timeInterval", "0"))
        image = sitk.ReadImage(tdc_file)
        hu_tuple = image.GetPixel(*idx_array)
        tdc_curve = []
        for index, value in enumerate(hu_tuple):
            time_index = Decimal(str(index)) * time_interval
            tdc_curve.append(float(time_index))
            tdc_curve.append(value)
        return RetCode.OK, tdc_curve


def save_mask_as_dicom(mask: np.ndarray, reference_dicom: pydicom.dataset.FileDataset, 
                      output_path: str, mask_value: int = 1):
    """
    将掩码保存为DICOM格式
    
    Args:
        mask: 掩码数组
        reference_dicom: 参考DICOM数据集
        output_path: 输出路径
        mask_value: 掩码值(1表示梗死区域,2表示非梗死区域)
    """
    # 创建新的DICOM数据集
    mask_ds = pydicom.Dataset()
    
    # 复制必要的DICOM标签
    for elem in reference_dicom:
        if elem.tag != (0x7fe0, 0x0010):  # 跳过PixelData
            mask_ds.add(elem)
    
    # 生成新的SOP Instance UID
    mask_ds.SOPInstanceUID = reference_dicom.SOPInstanceUID
    mask_ds.SeriesInstanceUID = reference_dicom.SeriesInstanceUID
    
    # 设置掩码特定的标签
    mask_ds.Modality = 'SEG'  # 分割对象
    mask_ds.SeriesDescription = f'Mask_{mask_value}'  # 描述
    
    # 设置图像相关参数
    mask_ds.SamplesPerPixel = 1
    mask_ds.PhotometricInterpretation = 'MONOCHROME2'
    mask_ds.BitsAllocated = 8
    mask_ds.BitsStored = 8
    mask_ds.HighBit = 7
    mask_ds.PixelRepresentation = 0
    
    # 保持原始图像尺寸
    mask_ds.Rows = mask.shape[0]
    mask_ds.Columns = mask.shape[1]
    # mask所有像素值*100
    mask *= 100
    # log.info(f"mask: {mask}")
    # mask_path = output_path.replace(".dcm", ".json")
    # with open(mask_path, "w") as f:
    #     json.dump(mask.tolist(), f)
    # 转换掩码为8位无符号整数
    # mask_pixels = (mask > 0).astype(np.uint8)
    # mask_pixels所有值都+1024
    # mask_pixels += 1024
    # 将掩码数据保存为文件，路径为output_path后缀替换为.json
    # json_path = output_path.replace(".dcm", "_pixel.json")
    # with open(json_path, "w") as f:
    #     json.dump(mask.tolist(), f)
    mask_ds.PixelData = mask.tobytes()
    # 同时记录PixelData的数据
    # json_path = output_path.replace(".dcm", "_pixel_data.json")
    # with open(json_path, "w") as f:
    #     json.dump(list(mask_ds.PixelData), f)
    
    # 添加文件元信息
    file_meta = pydicom.Dataset()
    file_meta.MediaStorageSOPClassUID = '1.2.840.10008.5.1.4.1.1.2'  # CT Image Storage
    file_meta.MediaStorageSOPInstanceUID = mask_ds.SOPInstanceUID
    file_meta.ImplementationClassUID = pydicom.uid.generate_uid()
    file_meta.TransferSyntaxUID = pydicom.uid.ExplicitVRLittleEndian
    
    # 创建完整的DICOM对象
    mask_dicom = pydicom.FileDataset(
        filename_or_obj="",
        dataset=mask_ds,
        file_meta=file_meta,
        preamble=b"\0" * 128
    )
    
    # 保存DICOM文件
    mask_dicom.is_little_endian = True
    mask_dicom.is_implicit_VR = False
    mask_dicom.save_as(output_path)

class WaterUptakeCalculator:
    """
    水摄取率计算器
    """
    def __init__(self, ct_images: List[pydicom.dataset.FileDataset], regions_data: Dict, 
                 study_instance_uid: str, series_instance_uid: str):
        """
        初始化计算器
        
        Args:
            ct_images: CT图像列表，每个元素是一个FileDataset对象
            regions_data: 包含梗死和非梗死区域坐标的字典
            study_instance_uid: 研究实例UID
            series_instance_uid: 序列实例UID
        """
        self.ct_images = ct_images
        self.regions_data = regions_data
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        
    def _get_region_density(self, ds: pydicom.dataset.FileDataset, coordinates: List[List[List[float]]], 
                           rescale_slope: float, rescale_intercept: float, mask_value: int) -> Tuple[float, int, np.ndarray]:
        """
        计算一个或多个区域的平均CT值（HU值）和像素数量，
        只计算HU值在20-80之间的像素，以去除脑沟脑回的影响
        
        Args:
            ds: DICOM图像
            coordinates: 输入的像素坐标列表
            mask_value: 掩码值, 1��示梗死区域, 2表示非梗死区域
        Returns:
            Tuple[float, int, np.ndarray]: (平均HU值, 像素数量, 掩码)
        """
            
        # 获取像素数据
        image = ds.pixel_array
        mask = np.zeros_like(image, dtype=np.uint8)
        if not coordinates:
            return 0.0, 0, mask
        
        height, width = image.shape
        
        for polygon_coords in coordinates:
            if not polygon_coords:
                continue
            
            # 直接使用输入的像素坐标，只需要转换为整数类型
            pixel_coords = np.array([[int(x), int(y)] for x, y in polygon_coords], dtype=np.int32)
            
            # 确保坐标在图像范围内
            pixel_coords[:, 0] = np.clip(pixel_coords[:, 0], 0, width - 1)
            pixel_coords[:, 1] = np.clip(pixel_coords[:, 1], 0, height - 1)
            
            # 使用像素坐标填充掩码
            cv2.fillPoly(mask, [pixel_coords], mask_value)
        
        # 获取掩码内的像素值
        masked_pixels = image[mask > 0]
        
        if len(masked_pixels) == 0:
            log.warning(f"警告：区域内没有有效像素")
            log.warning(f"图像尺寸: {image.shape}")
            log.warning(f"像素坐标范围: x[{np.min(pixel_coords[:,0])}-{np.max(pixel_coords[:,0])}], "
                       f"y[{np.min(pixel_coords[:,1])}-{np.max(pixel_coords[:,1])}]")
            return 0.0, 0, mask
        
        # 将原始像素值转换为HU值
        hu_values = masked_pixels * rescale_slope + rescale_intercept
        
        # 过滤HU值在20-80之间的像素
        valid_mask = (hu_values >= 20) & (hu_values <= 80)
        filtered_hu_values = hu_values[valid_mask]
        
        if len(filtered_hu_values) == 0:
            log.warning(f"警告：区域内没有HU值在20-80之间的有效像素")
            return 0.0, 0, mask
        
        return float(np.mean(filtered_hu_values)), len(filtered_hu_values), mask
        
    def _convert_dicom_value(self, value):
        """
        将DICOM值转换为可JSON序列化的类型
        
        Args:
            value: DICOM值
            
        Returns:
            转换后的值
        """
        if value == "":
            return "N/A"
        
        # 处理 MultiValue 类型
        if hasattr(value, '__iter__') and not isinstance(value, (str, bytes)):
            return [str(x) if hasattr(x, '__str__') else "N/A" for x in value]
        
        # 处理其他类型
        try:
            return str(value) if hasattr(value, '__str__') else "N/A"
        except:
            return "N/A"

    def calculate_water_uptake(self) -> Dict[str, float]:
        """
        使用密度法计算水摄取率，使用像素数量加权平均
        
        Returns:
            包含密度法计算结果的字典
        """
        total_infarct_density = 0.0
        total_normal_density = 0.0
        total_infarct_pixels = 0
        total_normal_pixels = 0
        
        # 创建保存目录
        save_dir = os.path.join(settings.DOCKER_MASKED_ROOT_DIR, 
                               self.study_instance_uid, 
                               self.series_instance_uid)
        if os.path.exists(save_dir):
            shutil.rmtree(save_dir)
        os.makedirs(save_dir, exist_ok=True)
        
        for idx, ds in enumerate(self.ct_images):
            # 获取当前切的区域坐标
            if (len(self.regions_data["infarction_area"]) < idx + 1 or 
                len(self.regions_data["non_infarction_area"]) < idx + 1):
                infarct_coords = []
                normal_coords = []
            else:
                infarct_coords = self.regions_data["infarction_area"][idx]
                normal_coords = self.regions_data["non_infarction_area"][idx]

            # 从DICOM获取必要的参数
            rescale_slope = ds.RescaleSlope
            rescale_intercept = ds.RescaleIntercept
            
            # 计算梗死和非梗死区域的密度
            infarct_density, infarct_pixels, infarct_mask = self._get_region_density(
                ds, infarct_coords, rescale_slope, rescale_intercept, 1
            )
            normal_density, normal_pixels, normal_mask = self._get_region_density(
                ds, normal_coords, rescale_slope, rescale_intercept, 2
            )
            
            # 合并掩码并保存为DICOM
            combined_mask = infarct_mask + normal_mask
            mask_path = os.path.join(save_dir, f"mask_{idx+1:03d}.dcm")
            save_mask_as_dicom(combined_mask, ds, mask_path)
            
            log.info(f"第{idx+1}张切片掩码已保存: {mask_path}")
            
            # 添加密度检查
            if infarct_density == 0 or normal_density == 0:
                log.warning(f"警告：第{idx}张切片的区域密度为0")
                continue
                
            total_infarct_density += infarct_density * infarct_pixels
            total_normal_density += normal_density * normal_pixels
            total_infarct_pixels += infarct_pixels
            total_normal_pixels += normal_pixels
        
        # 计算水摄取率
        if total_infarct_pixels <= 0 or total_normal_pixels <= 0:
            log.error("错误：总像素数为0，无法计算密度")
            return {
                "water_uptake": 0.0,
                "infarct_density": 0.0,
                "normal_density": 0.0
            }
            
        avg_infarct_density = total_infarct_density / total_infarct_pixels
        avg_normal_density = total_normal_density / total_normal_pixels
        
        if avg_normal_density == 0:
            log.error("错误：正常区域平均密度为0")
            density_uptake = 0.0
        else:
            density_uptake = 1 - (avg_infarct_density / avg_normal_density)
            density_uptake = format(density_uptake, '.3f')
        
        return {
            "water_uptake": density_uptake,
            "infarct_density": format(avg_infarct_density, '.3f'),
            "normal_density": format(avg_normal_density, '.3f')
        }

def read_json_file(file_path: str) -> Dict:
    with open(file_path, "r") as file:
        return json.load(file)

def load_dicom_images(dicom_dir: str) -> List[pydicom.dataset.FileDataset]:
    """
    读取dicom文件夹中的所有图像
    
    Args:
        dicom_dir: dicom文件夹路径
        
    Returns:
        包含所有dicom图像数据的列表
    """
    ct_images = []
    try:
        # 遍历dicom目录下的所有文件
        for file in os.listdir(dicom_dir):
            if file.endswith('.dcm'):
                file_path = os.path.join(dicom_dir, file)
                # 读取dicom文件
                ds = pydicom.dcmread(file_path)
                ct_images.append(ds)
        return ct_images
    except Exception as e:
        print(f"读取DICOM文件时出错: {str(e)}")
        return []
    

class CenterLineService:
    def __init__(self, study_instance_uid: str, series_instance_uid: str):
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid

    def send_center_line_task(self):
        """
        发送中心线任务
        """
        message = {
            "studyInstanceUID": self.study_instance_uid,
            "seriesInstanceUID": self.series_instance_uid,
            "algorithmType": "center_line",
            "resourceData": {},
            "callback": False,
            "taskType": SystemConfigUtils().getConfigValue(code="algorithmProcessMode", def_value="1")
        }
        try:
            RabbitMQProducer.simple_send(queue_name=settings.TYPE_QUEUE_DICT["center_line"], message=message)
        except Exception as e:
            log.error(f"发送中心线任务时发生错误: {str(e)}", exc_info=True)
            return RetCode.SERIES_PUSH_ERROR


def sort_ct_images_by_instance_number(ct_images: List[pydicom.dataset.FileDataset], series_instance_uid: str) -> List[pydicom.dataset.FileDataset]:
    """
    根据CallBackDICOM的instance_number对CT图像进行排序
    
    Args:
        ct_images: CT图像列表
        series_instance_uid: 序列UID
        
    Returns:
        排序后的CT图像列表
    """
    try:
        # 获取该序列的所有CallBackDICOM记录，按instance_number排序
        dicom_records = CallBackDICOM.objects.filter(
            series_instance_uid=series_instance_uid
        ).order_by('instance_number')
        
        # 创建SOP Instance UID到instance number的映射
        instance_number_map = {
            record.sop_instance_uid: record.instance_number 
            for record in dicom_records
        }
        
        # 根据instance number排序ct_images
        sorted_images = sorted(
            ct_images,
            key=lambda x: instance_number_map.get(x.SOPInstanceUID, float('inf'))
        )
        
        log.info(f"已对序列 {series_instance_uid} 的 {len(sorted_images)} 张图像按instance number排序")
        return sorted_images
        
    except Exception as e:
        log.error(f"对CT图像排序时发生错误: {str(e)}", exc_info=True)
        return ct_images  # 发生错误时返回原始列表