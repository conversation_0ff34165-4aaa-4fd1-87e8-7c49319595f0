#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : models
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/18 14:23
"""
import datetime

from django.db import models

from server.common.code import Const
# Create your models here.
from server.study.models import Study


class Series(models.Model):
    id = models.CharField(primary_key=True, max_length=255)  # 长度与旧表保持一致
    hospital_uid = models.CharField(max_length=64, null=False, default="0", verbose_name="医院标识")
    series_instance_uid = models.CharField(max_length=128, unique=True, null=False, default="", verbose_name="序列标识")
    modality = models.Char<PERSON>ield(max_length=16, null=False, default="", verbose_name="设备类型")
    series_description = models.CharField(max_length=255, null=False, default="", verbose_name="序列描述")
    series_number = models.Char<PERSON><PERSON>(max_length=16, null=False, default="", verbose_name="序列编号")
    series_date = models.CharField(max_length=8, null=False, default="", verbose_name="序列日期")
    series_time = models.CharField(max_length=16, null=False, default="", verbose_name="序列时间")
    body_part_examined = models.CharField(max_length=32, null=False, default="", verbose_name="检查部位")
    slice_thickness = models.CharField(max_length=16, null=False, default="", verbose_name="层厚")
    type = models.CharField(max_length=32, null=False, default="", verbose_name="序列类型")
    downloaded = models.BooleanField(null=False, default=False, verbose_name="下载状态")
    finish_percent = models.SmallIntegerField(null=False, default=0, verbose_name="完成进度")
    thumbnail = models.CharField(max_length=255, null=False, default="", verbose_name="缩略图")
    thumbnail_path = models.CharField(max_length=512, null=False, default="", verbose_name="缩略图地址")
    original_series = models.CharField(max_length=128, null=False, default="", verbose_name="原始序列")
    orthanc_id = models.CharField(max_length=255, null=False, default="", verbose_name="Orthanc标识")
    study = models.ForeignKey(Study, on_delete=models.CASCADE, null=False, verbose_name="所属检查")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    # add 2022-11-04
    geapi_status = models.SmallIntegerField(blank=True, null=True, verbose_name="ge申请资源状态")

    class Meta:
        db_table = 't_series'
        verbose_name = '序列信息'
        verbose_name_plural = 'series'

    def is_original(self):
        return self.type in [Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA]

    def to_dict(self):
        return dict(id=self.id, hospitalUID=self.hospital_uid, seriesInstanceUID=self.series_instance_uid,
                    seriesDescription=self.series_description, modality=self.modality,
                    bodyPartExamined=self.body_part_examined, seriesNumber=self.series_number,
                    seriesDate=self.series_date, seriesTime=self.series_time, sliceThickness=self.slice_thickness,
                    type="result" if "_" in self.type else self.type, thumbnail=self.thumbnail,
                    thumbnailPath=self.thumbnail_path, sourceSeries=self.original_series)


class SeriesAlgorithm(models.Model):
    """
    序列匹配算法信息，不包含匹配的算法类型字段，类型字段在Series中
    """
    id = models.CharField(primary_key=True, max_length=255)  # 长度与旧表保持一致
    series_instance_uid = models.CharField(max_length=128, null=False, default="", verbose_name="序列标识")
    comment = models.TextField(max_length=1024, null=False, default="", verbose_name="备注")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    class Meta:
        db_table = 't_series_algorithm'
        verbose_name = '序列匹配算法信息'
        verbose_name_plural = 'series_algorithm'


class SeriesSplit(models.Model):
    id = models.CharField(primary_key=True, max_length=255)  # 长度与旧表保持一致
    series_instance_uid = models.CharField(max_length=128, null=False, default="", verbose_name="序列标识")
    original_series = models.CharField(max_length=128, null=False, default="", verbose_name="原始序列")
    image_number = models.IntegerField(null=False, default="0", verbose_name="图像数量")
    type = models.SmallIntegerField(null=False, default="0", verbose_name="拆分类型")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    class Meta:
        db_table = 't_series_split'
        verbose_name = '序列拆分信息'
        verbose_name_plural = 'series_split'


class FeatureMap(models.Model):
    id = models.CharField(primary_key=True, max_length=255)  # 长度与旧表保持一致
    study_instance_uid = models.CharField(max_length=128, null=False, verbose_name="检查标识")
    series_instance_uid = models.CharField(max_length=128, null=False, verbose_name="序列标识")
    type = models.CharField(max_length=16, null=False, default="", verbose_name="参数图类型")
    window_width = models.CharField(max_length=32, null=False, default="", verbose_name="窗宽")
    window_level = models.CharField(max_length=32, null=False, default="", verbose_name="窗位")
    path = models.CharField(max_length=512, null=False, default="", verbose_name="保存路径")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    class Meta:
        db_table = "t_feature_map"
        verbose_name = "参数图"
        verbose_name_plural = "featureMaps"

    def to_dict(self):
        return dict(windowWidth=self.window_width, windowLevel=self.window_level, path=self.path)
