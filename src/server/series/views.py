#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : views
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/18 14:23
"""
import logging
import json
from datetime import datetime
import os

from django.conf import settings

from server.common.mongoConnector import MongoDB
from server.series.models import Series
# Create your views here.
from server.common.base import check_auth_code
from server.common.code import RetCode, Const
from server.common.utils import get_current_time
from server.common.views import BaseView
from server.series.service import CenterLineService, SeriesCallbackService, SeriesImageService, SeriesResultService, AlgoResultService, WaterUptakeCalculator, load_dicom_images, sort_ct_images_by_instance_number
from server.study.models import Study

log = logging.getLogger("django")


class SeriesCallbackView(BaseView):
    """序列回调重构"""

    def post(self, request):
        """
        序列回调

        :param request:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", Const.DEFAULT_HOSPITAL_UID)
        hospital_name = request_body.get("hospitalName", "")
        series_orthanc_id = request_body.get("seriesId", None)
        tags = request_body.get("tags", None)
        if not series_orthanc_id:
            return self.of(RetCode.SERIES_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.SERIES_TAGS_IS_EMPTY)
        series_instance_uid = tags.get("SeriesInstanceUID", "")
        if not series_instance_uid:
            return self.of(RetCode.SERIES_INSTANCE_UID_IS_EMPTY)
        if tags.get("Manufacturer", "") == Const.ALGORITHM_RESULT_MANUFACTURER:
            log.info("Series[callback] > series:{}, ignore result callback".format(series_instance_uid))
            return self.ok(message="result callback")
        log.info("Series[{}] > start callback".format(series_instance_uid))
        callback_service = SeriesCallbackService(series_orthanc_id, tags, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        log.info("Series[callback] > response: {}".format(ret_code.msg))
        return self.of(ret_code)


class SeriesImageView(BaseView):

    def get(self, request, series_instance_uid):
        """
        获取序列下图像

        :param request:
        :param series_instance_uid: 序列UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.debug("Series[image] > seriesInstanceUID:{}, get images".format(series_instance_uid))
        ret_code, data = SeriesImageService.get_image(series_instance_uid)
        return self.of(ret_code, data=data)


class SeriesResultView(BaseView):

    def get(self, request, series_instance_uid):
        """
        获取算法结果

        :param request:
        :param series_instance_uid: 序列UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        ret_code, data = SeriesResultService.get_result(series_instance_uid)
        return self.of(ret_code, data=data)

    def put(self, request, series_instance_uid):
        """
        更新算法结果

        :param request:
        :param series_instance_uid: 序列UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        body = request.PUT
        log.info("Series[result] > seriesInstanceUID:{}, body:{}, update text result".format(series_instance_uid, body))
        ret_code = SeriesResultService.update_result(series_instance_uid, body)
        return self.of(ret_code)


class SeriesCprView(BaseView):

    def get(self, request, series_instance_uid):
        """
        血管段查询

        :param request:
        :param series_instance_uid:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Series[cpr] > seriesInstanceUID:{}, get vessel segmentation".format(series_instance_uid))
        ret_code, data = AlgoResultService.get_cpr(series_instance_uid)
        return self.of(ret_code, data=data)


class SeriesTdcView(BaseView):

    def get(self, request, series_id):
        """
        血管段查询

        :param request:
        :param series_id: 序列ID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        params = request.GET
        idx = params.get("idx", "")
        log.info("Series[TDC] > seriesId:{}, idx:{}, get TDC curve".format(series_id, idx))
        if not series_id or not idx:
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        idx_array = idx.split(",")
        if len(idx_array) != 3:
            log.info("Series[TDC] > invalid idx: {}".format(idx))
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        series = Series.objects.filter(id=series_id).first()
        if not series:
            log.info("Series[TDC] > series not found: {}".format(series_id))
            return self.of(RetCode.SERIES_NOT_FOUND)
        if series.type != Const.ALGORITHM_TYPE_CTP:
            log.info("Series[TDC] > invalid series : {}".format(series_id))
            return self.of(RetCode.SERIES_INVALID_ALGORITHM_TYPE)
        idx_array = [int(i) for i in idx_array]
        ret_code, data = AlgoResultService.get_tdc(series, idx_array)
        return self.of(ret_code, data=data)


class CenterLineView(BaseView):
    def get(self, request, series_instance_uid):
        """获取序列的中心线数据
        
        Args:
            request: HTTP请求对象
            series_instance_uid: 序列UID
            
        Returns:
            JsonResponse: 包含中心线数据
        """
        try:
            # 验证用户权限
            creator = check_auth_code(request)
            if not creator:
                return self.of(RetCode.UNAUTHORIZED)

            # 查询中心线数据
            mongodb = MongoDB()
            center_line_data = mongodb.query_one(
                series_instance_uid,
                'center_line',
                'series_instance_uid'
            )

            if not center_line_data:
                return self.ok(
                    status=False,
                    message="未找到中心线数据",
                    data=None
                )

            return self.ok(
                message="成功获取中心线数据",
                data={
                    "study_instance_uid": center_line_data.get("study_instance_uid"),
                    "series_instance_uid": center_line_data.get("series_instance_uid"),
                    "center_line": center_line_data.get("value_fronted", []),  # 使用原始格式的数据
                    "create_time": center_line_data.get("create_time"),
                    "update_time": center_line_data.get("update_time"),
                    "_id": center_line_data.get("_id")
                }
            )

        except Exception as e:
            log.error(f"获取中心线数据时发生错误: {str(e)}", exc_info=True)
            return self.ok(status=False, message=f"获取中心线数据时发生错误")

    def post(self, request, series_instance_uid):
        """创建/更新中心线数据
        
        Args:
            request: HTTP请求对象
            series_instance_uid: 序列UID
            
        Returns:
            JsonResponse: 包含任务启动状态
        """
        try:
            # 验证用户权限
            creator = check_auth_code(request)
            if not creator:
                return self.of(RetCode.UNAUTHORIZED)

            # 解析请求数据
            try:
                request_data = json.loads(request.body)
                study_instance_uid = request_data.get('study_instance_uid')
            except json.JSONDecodeError:
                return self.ok(status=False, message="请求数据格式错误")

            # 验证必要参数
            if not study_instance_uid:
                return self.ok(status=False, message="缺少必要参数")
            
            # 发送中心线任务
            center_line_service = CenterLineService(study_instance_uid, series_instance_uid)
            center_line_service.send_center_line_task() 

            return self.ok(message="创建中心线任务成功")

        except Exception as e:
            log.error(f"保存中心线数据时发生错误: {str(e)}", exc_info=True)
            return self.ok(status=False, message=f"保存中心线数据时发生错误")


class SeriesWaterUptakeRateView(BaseView):
    def get(self, request, series_instance_uid):
        """获取序列的水摄取率数据
        
        Args:
            request: HTTP请求对象
            series_instance_uid: 序列UID
            
        Returns:
            JsonResponse: 包含水摄取率数据
        """
        try:
            # 验证用户权限
            creator = check_auth_code(request)
            if not creator:
                return self.of(RetCode.UNAUTHORIZED)

            # 查询水摄取率数据
            mongodb = MongoDB()
            water_uptake_rate = mongodb.query_one_new(
                series_instance_uid,
                'water_uptake_rate', 
                'series_instance_uid',
                projection={
                    'infarction_area': 0,
                    'non_infarction_area': 0
                }
            )

            if not water_uptake_rate:
                return self.ok(
                    status=False,
                    message="未找到水摄取率数据",
                    data=None
                )

            return self.ok(
                message="成功获取水摄取率数据",
                data=water_uptake_rate
            )

        except Exception as e:
            log.error(f"获取水摄取率数据时发生错误: {str(e)}", exc_info=True)
            return self.ok(status=False, message=f"获取水摄取率数据时发生错误: {str(e)}")

    def post(self, request, series_instance_uid):
        """创建/更新水摄取率数据
        
        Args:
            request: HTTP请求对象
            series_instance_uid: 序列UID
            
        Returns:
            JsonResponse: 包含处理结果
        """
        try:
            # 验证用户权限
            creator = check_auth_code(request)
            if not creator:
                return self.of(RetCode.UNAUTHORIZED)

            # 解析请求数据
            try:
                request_data = json.loads(request.body)
                study_instance_uid = request_data.get('study_instance_uid')
                infarction_area_fronted = request_data.get('infarction_area_fronted', [])
                non_infarction_area_fronted = request_data.get('non_infarction_area_fronted', [])
                center_line_fronted = request_data.get('center_line_fronted', [])  # 新增中心线参数
            except json.JSONDecodeError:
                return self.ok(status=False, message="请求数据格式错误")

            # 验证必要参数
            if not all([study_instance_uid, infarction_area_fronted, non_infarction_area_fronted]):
                return self.ok(status=False, message="缺少必要参数")

            # 在转换格式之前添加校验逻辑
            if len(infarction_area_fronted) != len(non_infarction_area_fronted):
                return self.ok(status=False, message="梗死区域和非梗死区域的图像数量不匹配")
            
            # 基于上面长度相等的条件下
            # 如果梗死区域和非梗死区域都只有一个元素并且该元素长度为0，则认为没有标记
            if len(infarction_area_fronted) == 1 and len(infarction_area_fronted[0]) == 0:
                return self.ok(status=False, message="必须标记梗死区域")

            # 验证每张图像的标记
            for frame_idx, (infarct_frame, non_infarct_frame) in enumerate(zip(infarction_area_fronted, non_infarction_area_fronted)):
                # 如果两个区域都没有标记，则认为没有标记
                if not infarct_frame and not non_infarct_frame:
                    continue
                if (not infarct_frame and non_infarct_frame) or (infarct_frame and not non_infarct_frame):
                    return self.ok(status=False, message=f"第 {frame_idx + 1} 张图像必须同时包含梗死区域和非梗死区域的标记")
                # 如果其中一个区域有标记，另一个区域必须也有标记
                if (len(infarct_frame) > 0 and len(non_infarct_frame) == 0) or \
                   (len(infarct_frame) == 0 and len(non_infarct_frame) > 0):
                    return self.ok(status=False, message=f"第 {frame_idx + 1} 张图像必须同时包含梗死区域和非梗死区域的标记")

            # 转换前端格式为后端存储格式
            def convert_fronted_to_backend(area_fronted):
                area_backend = []
                for idx, frame_points in enumerate(area_fronted):
                    frame_coords = []
                    if frame_points is None:
                        area_backend.append(frame_coords)
                        continue
                    for area_points in frame_points:
                        # 从handles.points中获取所有点坐标
                        points = area_points['handles']['points']
                        coords = [(float(point['x']), float(point['y'])) for point in points]
                        frame_coords.append(coords)
                    area_backend.append(frame_coords)
                return area_backend

            try:
                # 转换格式
                infarction_area = convert_fronted_to_backend(infarction_area_fronted)
                non_infarction_area = convert_fronted_to_backend(non_infarction_area_fronted)
            except ValueError as e:
                return self.ok(status=False, message="坐标格式转换失败")

            regions_data = {
                "infarction_area": infarction_area,
                "non_infarction_area": non_infarction_area
            }

            # 查询相关数据
            series = Series.objects.filter(series_instance_uid=series_instance_uid).first()
            study = Study.objects.filter(study_instance_uid=study_instance_uid).first()

            if not series or not study:
                return self.ok(status=False, message="未找到相关序列或检查信息")

            # 确定DICOM文件路径
            is_toshiba_ctp = (study.toshiba and series.type == Const.ALGORITHM_TYPE_CTP)
            dcm_dir = os.path.join(settings.DOCKER_CTP_ROOT_DIR, 
                                   study_instance_uid if is_toshiba_ctp else series_instance_uid)
            ct_images = load_dicom_images(dcm_dir)
            # 根据instance number排序图像
            ct_images = sort_ct_images_by_instance_number(ct_images, series_instance_uid)
            mongodb = MongoDB()
            current_time = get_current_time()
            # 1. 更新中心线数据
            def convert_center_line_to_backend(center_line_fronted):
                """将前端中心线格式转换为后端存储格式"""
                center_line_backend = []
                for line in center_line_fronted:
                    if 'handles' in line:
                        start_point = [line['handles']['start']['x'], line['handles']['start']['y']]
                        end_point = [line['handles']['end']['x'], line['handles']['end']['y']]
                        center_line_backend.append([start_point, end_point])
                return center_line_backend
            center_line_data = {
                "study_instance_uid": study_instance_uid,
                "series_instance_uid": series_instance_uid,
                "value_fronted": center_line_fronted,
                "value": convert_center_line_to_backend(center_line_fronted),
                "update_time": current_time
            }

            existing_center_line = mongodb.query_one(
                series_instance_uid,
                'center_line',
                'series_instance_uid'
            )

            if existing_center_line:
                center_line_data['create_time'] = existing_center_line['create_time']
            else:
                center_line_data['create_time'] = current_time

            mongodb.update_one(
                'center_line',
                {"series_instance_uid": series_instance_uid},
                center_line_data,
                upsert=True
            )
            
            # 计算水摄取率
            # 创建计算器实例并计算水摄取率
            calculator = WaterUptakeCalculator(ct_images, regions_data, study_instance_uid, series_instance_uid)
            
            try:
                water_uptake_value = calculator.calculate_water_uptake()["water_uptake"]
                # water_uptake_value = 0
            except Exception as e:
                log.error(f"计算水摄取率时发生错误: {str(e)}", exc_info=True)
                return self.ok(status=False, message=f"计算水摄取率失败")

            # 准备保存的数据
            water_uptake_data = {
                "study_instance_uid": study_instance_uid,
                "series_instance_uid": series_instance_uid,
                "infarction_area": infarction_area,
                "non_infarction_area": non_infarction_area,
                "infarction_area_fronted": infarction_area_fronted,
                "non_infarction_area_fronted": non_infarction_area_fronted,
                "value": water_uptake_value,
                "update_time": current_time
            }

            # 保存到MongoDB
            mongodb = MongoDB()
            existing_record = mongodb.query_one(
                series_instance_uid,
                'water_uptake_rate',
                'series_instance_uid'
            )

            if existing_record:
                water_uptake_data['create_time'] = existing_record['create_time']
            else:
                water_uptake_data['create_time'] = current_time

            mongodb.update_one(
                'water_uptake_rate',
                {"series_instance_uid": series_instance_uid},
                water_uptake_data,
                upsert=True
            )

            return self.ok(
                message="水摄取率数据保存成功",
                data=water_uptake_data
            )

        except Exception as e:
            log.error(f"保存水摄取率数据时发生错误: {str(e)}", exc_info=True)
            return self.ok(status=False, message=f"保存水摄取率数据时发生错误")
