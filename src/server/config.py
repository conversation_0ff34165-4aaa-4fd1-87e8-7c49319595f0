import os

_env = os.environ
DEBUG = True
DB_HOST = _env.get('DB_HOST', '************')
DB_USER = _env.get('DB_USER', 'root')
DB_PASSWORD = _env.get('DB_PASSWORD', 'UnionStrong@2020')
DB_PORT = _env.get('DB_PORT', "3310")
DB_DOCKER_EXPOSE_PORT = _env.get("DB_DOCKER_EXPOSE_PORT", "3310")
MONGODB_HOST = _env.get('MONGODB_HOST', '************')
MONGODB_PORT = _env.get('MONGODB_PORT', '27018')
MONGODB_USER = _env.get('MONGO_INITDB_ROOT_USERNAME', "unionstrong")
MONGODB_PASSWORD = _env.get('MONGO_INITDB_ROOT_PASSWORD', "UnionStrong@2020")
# pacs 服务器
ORTHANC_HOST = _env.get("ORTHANC_HOST", "************")
ORTHANC_AET = _env.get("ORTHANC_AET", "DockerOrthanc")
ORTHANC_PORT = int(_env.get("ORTHANC_PORT", 4242))
ORTHANC_WEB_PORT = int(_env.get("ORTHANC_WEB_PORT", 8044))
ORTHANC_WADO_PORT = int(_env.get("ORTHANC_WADO_PORT", 8044))
ORTHANC_Docker_PORT = int(_env.get("ORTHANC_WADO_PORT", 8044))
ORTHANC_WADO_USERNAME = _env.get("ORTHANC_WADO_USERNAME", "unionstrong")
ORTHANC_WADO_PASSWORD = _env.get("ORTHANC_WADO_PASSWORD", "UnionStrong@2020")
# Pacs本地服务器：
LOCAL_HOST = _env.get("LOCAL_HOST", "************")
LOCAL_AET = _env.get("LOCAL_AET", "UNIONSTRONG")
LOCAL_PORT = _env.get("LOCAL_PORT", 1178)
PACS_SERVER_IP = _env.get("PACS_SERVER_IP", "************")
MQ_HOST = _env.get("MQ_HOST", "************")
MQ_PORT = _env.get("MQ_PORT", 5673)
MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")
MQ_WEB_PORT = _env.get("MQ_MANAGE_PORT", 15673)
NGINX_PORT = _env.get("NGINX_PORT", 4224)

# orthanc 配置文件路径
ORTHANC_CONFIG_FILE = _env.get("ORTHANC_CONFIG_FILE", "/code/orthanc.json")

# GE回调开关
GE_CALLBACK = _env.get("GE_CALLBACK_START", False)
GE_CALLBACK = True if GE_CALLBACK == "1" else False

TYPE_QUEUE_DICT = dict(
    ctp="transport_algorithm_task",
    aspects="transport_algorithm_task",
    cta="transport_algorithm_task",
    center_line="transport_algorithm_task"
)

# 序列描述与算法映射
SERIES_DESC_ALGORITHM_MAPPING = {
    "USC-UGuard ASPECTS Summary": "aspects",
    "USC-UGuard ICH Summary": "aspects",

    "3D ASPECTS MASK USC": "aspects",
    "3D PC-ASPECTS SUMMARY USC": "aspects",
    "3D ASPECTS SUMMARY USC": "aspects",
    "HEMORRHAGE SUMMARY USC": "aspects",
    "HEMORRHAGE MASK USC": "aspects",


    "USC-UGuard CTA Volume Rendering": "cta",
    "USC-UGuard CTA W/vessel extraction": "cta",
    "USC-UGuard CTA W/bone Removed": "cta",
    "USC-UGuard CTA w/Vessel Mask": "cta",
    "USC-UGuard CTA MIP": "cta",

    "collateral circulation USC": "cta",
    "USC-UGuard CTA Volume Rendering AP": "cta",
    "USC-UGuard CTA Volume Rendering LR": "cta",
    "USC-UGuard CTA Volume MIP AP": "cta",
    "USC-UGuard CTA Volume MIP LR": "cta",
    "USC-UGuard CTA Volume Rendering Head AP": "cta",
    "USC-UGuard CTA Volume Rendering Head LR": "cta",
    "USC-UGuard CTA Volume MIP Head AP": "cta",
    "USC-UGuard CTA Volume MIP Head LR": "cta",

    "USC-UGuard CTP Summary": "ctp",
    "USC-UGuard CTP Total Summary": "ctp",
    "USC-UGuard AIF-VOF Location": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored CBF": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored CBV": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored MTT": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored Tmax": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored TTP": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored PS": "ctp",
    "USC-UGuard CTP MIP": "ctp",
    "USC-UGuard CTP/CTA Volume Rendering": "ctp",
    "USC-UGuard Perfusion Parameter Maps Colored": "ctp",
    "USC-UGuard CTP Total MIP": "ctp",
}
# monitor_list = {"cloud_platform_mongodb": "算法数据库服务",
#                 "algorithm_ctp": "算法ctp服务",
#                 "cloud_platform_mq_comsume": "算法处理结果服务",
#                 "cloud_platform_pacs_local_server": "被动接收服务",
#                 "nginx_cloud_local_platform": "工作站前端服务",
#                 "cloud_platform_api": "工作站后台服务",
#                 "cloud_platform_rabbitmq": "消息中间键服务",
#                 "cloud_platform_pacs": "dicom存储数据服务",
#                 "cloud_platform_mysql": "基础数据库服务",
#                 "algorithm_aspects": "算法aspects服务",
#                 "cloud_platform_download_consumer_server": "dicom下载服务"}
monitor_list = {"ugs-mongodb": "算法数据库服务",
                "ugs-result": "算法处理结果服务",
                "ugs-api": "接口服务",
                # "algorithm_ctp_aspects": "算法处理服务",
                "ugs-nginx": "工作站前端服务",
                # "cloud_platform_api": "工作站后台服务",
                "ugs-rabbitmq": "消息中间件服务",
                "ugs-pacs": "dicom存储数据服务",
                "ugs-mysql": "基础数据库服务",
                "ugs-transport": "算法任务中转服务",
                "ugs-ctp": "ctp算法集成服务",
                "ugs-aspects": "aspect算法集成服务",
                "ugs-cta": "cta算法集成服务",
                "ugs-delete": "删除服务"
                }
monitor_port = 4683
monitor_ip = _env.get("PACS_SERVER_IP", "************")
# 服务名称和日志路径的对应关系， root_dir  /data/ctpdata/{service_path}
UGS_SERVICES_THIRD_PARTY = ["ugs-mysql", "ugs-rabbitmq", "ugs-pacs", "ugs-nginx", "ugs-mongodb"]
UGS_SERVICES_PLATFORM = ["ugs-api", "ugs-transport", "ugs-result", "ugs-delete", "ugs-aspects", "ugs-ctp", "ugs-cta"]


C_Find_Parameter_List = [
    "PatientName",
    "PatientID",
    "PatientSex",
    "ProtocolName",
    "StudyInstanceUID",
    "SeriesInstanceUID",
    "PatientAge",
    "PatientWeight",
    "PatientBirthDate",
    "StudyDescription",
    "StudyDate",
    "StudyTime",
    "SeriesDescription",
    "SeriesDate",
    "SeriesTime",
    "SeriesNumber",
    "InstitutionName",
    "SOPInstanceUID",
    "NumberOfFrames",
    "SliceThickness",
    "PixelSpacing",
    "Rows",
    "Columns",
    "SliceLocation",
    "Modality",
    "AcquisitionType",
    "StationName",
    "InstanceNumber",
    "Manufacturer",
    "ManufacturerModelName"
]

ERROR_CODE = {
    50002: "未能完成计算，请检查数据格式",
    50003: "未能完成计算，请检查数据格式",
    50009: "未能完成计算，请检查数据格式",
}

Algorithm_ERROR = {
    200: "aspect 计算返回结果异常",
    300: "aspect数据为空",
    400: "aspect 计算该医学图像缺少层厚标签",
    500: "算法异常",
    600: "ctp 生成医学图像文件为空",
    800: "ctp 生成医学图像上传失败",
    900: "ctp 生成图失败",
    501: "报告回传失败",
    502: "报告保存失败",
    504: '获取超时',
    505: "cta 算法异常",
    850: 'pacs 查询失败'
}
DOCKER_CTP_ROOT_DIR = _env.get("DOCKER_CTP_ROOT_DIR", "/code/data/dcm")
DOCKER_DATA_BASE_DIR = _env.get("DOCKER_DATA_BASE_DIR", "/code/data")
# if not os.path.exists(DOCKER_CTP_ROOT_DIR):sudo
#     os.mkdir(DOCKER_CTP_ROOT_DIR)
FILE_PROCESS_TIMEOUT = int(_env.get("FILE_PROCESS_TIMEOUT", 60))

ORTHANC_STORAGE_PATH = _env.get("ORTHANC_STORAGE_PATH", "/clouddata/pacs/db-v6")
DOCKER_MASKED_ROOT_DIR = _env.get("DOCKER_MASKED_ROOT_DIR", "/code/data/masked")
