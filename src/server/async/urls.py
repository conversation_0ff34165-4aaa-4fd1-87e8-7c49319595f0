from django.urls import path

from .views import PacsListView, PacsImageList, ImagePacsView, ExchangeImageView, MonitorView, \
    PACSServerView, PACSRemoteDownloadView, CpuInfoView, \
    ClearPeriodDataView, OrthancCFindView, OrthancCMoveView, TransmissionTimeView, ConfigSetView, WaitTimeSetView, \
    MonitorLogView, ReportDicomUploadView, ClearPatientDataView

urlpatterns = [
    path('pacslist/', PacsListView.as_view()),
    path('pacsimg/', PacsImageList.as_view()),
    path('pacsimgupload/', ImagePacsView.as_view()),
    path('image/', ExchangeImageView.as_view()),
    path('monitor/', MonitorView.as_view()),
    path("pacsserver/", PACSServerView.as_view()),
    path("pacsremotedownload/", PACSRemoteDownloadView.as_view()),
    path("cpuinfo/", CpuInfoView.as_view()),
    # 数据清理（时间区间）
    path("clearperioddata/", ClearPeriodDataView.as_view()),
    # 清除病人数据
    path("clearpatientdata/", ClearPatientDataView.as_view()),
    path("orthanccfind/", OrthancCFindView.as_view()),
    path("orthanccmove/", OrthancCMoveView.as_view()),
    # 影像传输时间接口
    path("transmissiontime/", TransmissionTimeView.as_view()),
    # 数据设置页查询和创建接口
    path("configfilter/", ConfigSetView.as_view()),
    # 数据设置页删除接口
    path("configfilter/<pk>", ConfigSetView.as_view()),
    # 等待时间设置接口
    path("waittime/", WaitTimeSetView.as_view()),
    # 服务日志接口
    path("monitor/logs/", MonitorLogView.as_view()),
    # dicom 报告回传
    path("reportdicomupload/", ReportDicomUploadView.as_view())
]
