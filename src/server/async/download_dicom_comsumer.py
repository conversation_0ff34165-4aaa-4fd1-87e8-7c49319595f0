import pika
import json
import os
import traceback

_env = os.environ
MQ_HOST = _env.get("MQ_HOST", "***********")
MQ_PORT = _env.get("MQ_PORT", 5673)
MQ_USERNAME = _env.get("MQ_USERNAME", "admin")
MQ_PASSWORD = _env.get("MQ_PASSWORD", "admin")
MQ_CONSUMER_NAME = _env.get("MQ_DICOM_NAME", "download_dicom_consumer")
MQ_SEND_NAME = _env.get("MQ_SEND_NAME", "download_dicom_client")
from server.common.utils import PACSUtil


class RabbitMQAPI:
    def __init__(self, queue_name=None, no_ack=False):
        self.queue_name = queue_name or MQ_CONSUMER_NAME
        self.no_ack = no_ack

    @property
    def no_ack(self):
        return self._no_ack

    @no_ack.setter
    def no_ack(self, value):
        if not isinstance(value, bool):
            raise ValueError("no_ack must be boolean")
        self._no_ack = value

    def build_connection(self, is_consumer=False, queue_name=None):
        queue_name = queue_name or self.queue_name
        credentials = pika.PlainCredentials(MQ_USERNAME, MQ_PASSWORD)
        if is_consumer:
            connection_dict = dict(host=MQ_HOST,
                                   port=MQ_PORT,
                                   virtual_host='/',
                                   credentials=credentials,
                                   heartbeat_interval=0)
        else:
            connection_dict = dict(host=MQ_HOST,
                                   port=MQ_PORT,
                                   credentials=credentials,
                                   blocked_connection_timeout=300
                                   )
        connection = pika.BlockingConnection(pika.ConnectionParameters(**connection_dict))
        channel = connection.channel()
        try:
            channel.queue_declare(queue=queue_name, durable=True)
        except:
            channel = connection.channel()
            channel.queue_delete(queue=queue_name)
            channel.queue_declare(queue=queue_name, durable=True)
        return channel, connection

    def process_data_callback(self, channel, method, properties, body):
        '''
        :param channel:
        :param method:
        :param properties:
        :param body:
        :return:
        '''
        if not self.no_ack:
            channel.basic_ack(delivery_tag=method.delivery_tag)

        pass

    def consumer(self):
        channel, connection = self.build_connection(is_consumer=True)
        channel.basic_consume(self.process_data_callback,
                              queue=self.queue_name,
                              no_ack=self.no_ack
                              )
        print('waiting for message To exit   press CTRL+C')
        channel.start_consuming()

    def producer(self, queue, body):
        channel, connection = self.build_connection(queue_name=queue)
        channel.basic_publish(exchange='',
                              routing_key=queue,
                              properties=pika.BasicProperties(
                                  content_type="application/json",
                                  delivery_mode=2
                              ),
                              body=json.dumps(body))
        connection.close()
        return True


class DownloadDICOMConsumer(RabbitMQAPI):
    def __init__(self, name=None, no_ack=False):
        super(DownloadDICOMConsumer, self).__init__(queue_name=name, no_ack=no_ack)
        super(DownloadDICOMConsumer, self).consumer()

    def process_data_callback(self, channel, method, properties, body):
        pacs_info, query_type, patient_info = self.data_body(body=body)
        init = PACSUtil(**pacs_info)
        download_target = init.retrieval_some_image_new(patient_info, queryRetrievel=query_type)
        for _, message in download_target:
            self.producer(queue=MQ_SEND_NAME, body=message)
        if not self.no_ack:
            channel.basic_ack(delivery_tag=method.delivery_tag)

    @staticmethod
    def data_body(body):
        """
        :param body:
        {
    "pacs_info": {
        "target_ip": "***********",
        "target_ae": "DockerOrthanc",
        "target_port": 4242
    },
    "query_type": "SERIES",
    "patient_info":{
        "StudyInstanceUID": "********.1107.5.1.4.53621.30000014070423572131200000418",
        "SeriesInstanceUID":"********.1107.5.1.4.53621.30000014070423244203100005720"
        }
}

        :return:

        """
        try:
            data = json.loads(body)
        except Exception:
            print(traceback.format_exc())
            return False, "body解析错误", None
        if isinstance(data, (tuple, list)):
            return False, "body解析错误", None
        pacs_info = data.get("pacs_info", None)
        patient_info = data.get("patient_info", None)
        query_type = data.get("query_type", "SERIES")
        return pacs_info, query_type, patient_info


class DownloadDICOMClient(RabbitMQAPI):
    def __init__(self, *args, name=None, **kwargs):
        super(DownloadDICOMClient, self).__init__(queue_name=name)
        self.is_batch = None
        self.data = args if args else kwargs

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, value):
        try:
            if isinstance(value, dict):
                self._data = value
                self.is_batch = False
            elif isinstance(value, (list, tuple)):
                self.is_batch = True
                self._data = value
            else:
                self._data = json.loads(value)
                self.is_batch = False
        except Exception:
            print(traceback.format_exc())
            self._data = None

    def send_message(self):
        if self.data:
            print(self.is_batch)
            if not self.is_batch:
                status = self.producer(queue=self.queue_name, body=self.data)
                print(status)
            else:
                for i in self.data:
                    status = self.producer(queue=self.queue_name, body=i)
                    print(status)
        else:
            print("error")


if __name__ == '__main__':
    DownloadDICOMConsumer()
