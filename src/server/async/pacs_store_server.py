import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
django.setup()
from pynetdicom import (AE, StoragePresentationContexts)
from pydicom.uid import (
    ExplicitVRLittleEndian,
    ImplicitVR<PERSON><PERSON><PERSON>ndian,
    DeflatedExplicitVR<PERSON>ittleEndian,
    ExplicitVRBigEndian,
    JPEGBaseline,
    JPEGExtended,
    JPEGLosslessP14,
    JPEGL<PERSON>less,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    JPEGLSLossy,
    JPEG2000<PERSON>ossless,
    JPEG2000,
    JPEG2000<PERSON>ulti<PERSON>omponentLossless,
    JPEG2000MultiComponent,
    R<PERSON><PERSON>ossless,
    UID
)

Trancfortanx = [
    ImplicitVR<PERSON>ittleEndian,
    ExplicitVRLittleEndian,
    ExplicitVRBigEndian,
    DeflatedExplicitVRLittleEndian,
    JPEGBaseline,
    JPEGExtended,
    JPEGL<PERSON>lessP14,
    JP<PERSON><PERSON><PERSON>less,
    JP<PERSON><PERSON><PERSON><PERSON><PERSON>,
    JP<PERSON><PERSON><PERSON><PERSON><PERSON>,
    JPEG<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>2000,
    JPEG2000Multi<PERSON>omponent<PERSON>ossless,
    <PERSON><PERSON>20<PERSON><PERSON><PERSON><PERSON><PERSON>omponent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
]
from pydicom.dataset import Dataset, FileDataset
import hashlib
import traceback
from server.algorithm.task import producer
from server.common.utils_v1 import PACSUtilNew
from server.algorithm.models import AlgorithmTask
import uuid
import datetime

_env = os.environ
MQ_HOST = _env.get("MQ_HOST", "***********")
MQ_PORT = _env.get("MQ_PORT", 5673)
MQ_USERNAME = _env.get("MQ_USERNAME", "admin")
MQ_PASSWORD = _env.get("MQ_PASSWORD", "admin")
MQ_ASPECT_QUEUE = "algorithm_send_aspect"
MQ_CTP_QUEUE = "algorithm_send_ctp"
ORTHANC_HOST = _env.get("ORTHANC_HOST", "***********")
ORTHANC_AET = _env.get("ORTHANC_AET", "DockerOrthanc")
ORTHANC_PORT = _env.get("ORTHANC_PORT", 4242)
LOCAL_AET = _env.get("LOCAL_AET", "SONGYOULI3")
LOCAL_HOST = _env.get("LOCAL_HOST", "***********")
LOCAL_PORT = _env.get("LOCAL_PORT", 1178)
PER_DICOM_TIMEOUT = _env.get("PER_DICOM_TIMEOUT", 60)


class NewAE(AE):
    def __init__(self):
        self.SERIES_KEY_MAP = dict()
        self.pacs_address = ORTHANC_HOST
        self.aet = ORTHANC_AET
        self.aet_port = ORTHANC_PORT
        self.local_ae = LOCAL_AET
        self.local_address = LOCAL_HOST
        self.local_port = LOCAL_PORT
        self.date_time = datetime.datetime.now()
        super(NewAE, self).__init__(ae_title=LOCAL_AET)

    def on_c_store(self, dataset, context, info):
        SeriesInstanceUID = dataset.get("SeriesInstanceUID", "")
        StudyInstanceUID = dataset.get("StudyInstanceUID", "")
        if StudyInstanceUID and SeriesInstanceUID:
            _md = hashlib.md5()
            _md.update(F"{StudyInstanceUID.strip()}{SeriesInstanceUID.strip()}".encode(encoding='utf-8'))
            if self.SERIES_KEY_MAP.get(_md.hexdigest(), None):
                self.SERIES_KEY_MAP[_md.hexdigest()].append(F"{SeriesInstanceUID}")
            else:
                if self.SERIES_KEY_MAP:
                    # 开始创建任务
                    self.plan_process()
                self.clear()
                self.SERIES_KEY_MAP[_md.hexdigest()] = [F"{SeriesInstanceUID}"]
            # 一旦接到数据上传orthanc
            file_meta = Dataset()
            file_meta.TransferSyntaxUID = context.transfer_syntax
            dataset.file_meta = file_meta
            _uid = UID(context.transfer_syntax)
            dataset.is_little_endian = True if _uid.is_little_endian else False
            dataset.is_implicit_VR = True if _uid.is_implicit_VR else False
            status, info = self.save_dicom_img(dataset)
            if not status:
                # 容错
                self.clear()
                pass
        else:
            self.clear()
        # meta = Dataset()
        # _uid = UID(context.transfer_syntax)
        # meta.TransferSyntaxUID = context.transfer_syntax
        #
        # file, dicom_info = self.makedir(dataset, file_path=self.file_path)
        # print("hhhhh*****", file, "******dicom****", dicom_info)
        # if not os.path.isfile(file):
        #     ds = FileDataset(file, dataset, file_meta=meta, preamble=b"\0" * 128,
        #                      is_little_endian=True if _uid.is_little_endian else False,
        #                      is_implicit_VR=True if _uid.is_implicit_VR else False)
        #     ds.save_as(file)
        #     # a = pydicom.read_file(file)
        #     # print(a)
        self.date_time = datetime.datetime.now()
        return 0x0000

    def clear(self):
        self.SERIES_KEY_MAP = dict()
        self.date_time = datetime.datetime.now()

    def plan_process(self):
        data_to_list = list(self.SERIES_KEY_MAP.values())[0]
        dicom_num = len(data_to_list)
        print("**************dicom num*********", dicom_num, "****", data_to_list[0])
        algorithm_type = "aspects"
        queue = MQ_ASPECT_QUEUE
        if dicom_num >= 300:
            # ctp
            algorithm_type = "ctp"
            queue = MQ_CTP_QUEUE
        if dicom_num != 0:
            task_uuid = str(uuid.uuid1())
            AlgorithmTask.objects.create(uuid=task_uuid,
                                         series_uid=data_to_list[0],
                                         algorithm_type=algorithm_type,
                                         user_id="admin")
            data = dict(SeriesInstanceUID=data_to_list[0],
                        task_uuid=task_uuid,
                        algorithm_type=algorithm_type
                        )
            producer(queue, body=data)

    @classmethod
    def start(cls):
        self = cls()
        ae = self
        ae.add_requested_context('1.2.840.10008.*******.2.1.2')
        transfer_syntaxes = Trancfortanx
        for context in StoragePresentationContexts:
            ae.add_supported_context(context.abstract_syntax, transfer_syntaxes)
        try:
            ae.start_server((self.local_address, self.local_port), block=False)
            return ae
        except Exception:
            print(traceback.format_exc())
            pass
        finally:
            pass

    def save_dicom_img(self, dataset):
        ae = AE(ae_title=self.local_ae)
        ae.requested_contexts = StoragePresentationContexts
        assoc = ae.associate(self.pacs_address, self.aet_port, ae_title=self.aet)
        value = None
        if assoc.is_established:
            value = assoc.send_c_store(dataset)
            if value:
                if value.get("Status") == 0:
                    assoc.release()
                    return True, dataset.get('SeriesInstanceUID', None)
                else:
                    assoc.release()
                    return False, None
            else:
                assoc.release()
                return False, None
        else:
            return False, None


if __name__ == '__main__':
    ae = NewAE.start()
    while True:
        now = datetime.datetime.now()
        end_time, data = ae.date_time, ae.SERIES_KEY_MAP
        if (now - end_time).total_seconds() > PER_DICOM_TIMEOUT and data and len(list(data.items())):
            print(now, "**************", end_time, "*******", (now - end_time).total_seconds())
            print("最后了**************")
            ae.plan_process()
            ae.clear()
