import json
import math
import os
import re
import traceback

import docker
import requests

import server.settings as settings
from server.common.utils import PACSUtil

requests.packages.urllib3.disable_warnings()
orthanc_port = str(settings.ORTHANC_WADO_PORT)

import logging
log = logging.getLogger("django")


class DockerMonitor:
    def __init__(self, ip=settings.monitor_ip, port=settings.monitor_port,
                 monitor_list=list(settings.monitor_list.keys())):
        if not os.path.exists(settings.client_crt_path) or not os.path.exists(settings.client_key_path):
            log.info("docker tls not found, please check path, crt:{}, key:{}".format(
                settings.client_crt_path, settings.client_key_path))
        ls_config = docker.tls.TLSConfig(
            client_cert=(settings.client_crt_path, settings.client_key_path)
        )
        self.client = docker.DockerClient(base_url="tcp://{ip}:{port}".format(ip=ip, port=str(port)), tls=ls_config)
        self.monitor_list = monitor_list

    def check_containers_server(self, filters=dict()):
        container_list = None
        data = []
        if self.monitor_list:
            container_list = self.client.containers.list(filters=dict(name=self.monitor_list), all=True)
        if filters:
            container_list = self.client.containers.list(filters=filters, all=True)
        if not container_list:
            container_list = self.client.containers.list(all=True)
        try:
            for container in container_list:
                attrs_dict = container.attrs
                if settings.monitor_list.get(attrs_dict["Name"].strip("/"), None):
                    data.append(dict(status=attrs_dict["State"]["Running"],
                                     container_name=attrs_dict["Name"].strip("/"),
                                     name=settings.monitor_list[attrs_dict["Name"].strip("/")],
                                     restart_count=attrs_dict["RestartCount"],
                                     container_port=attrs_dict["NetworkSettings"]["Ports"],
                                     status_error=attrs_dict["State"]["Error"],
                                     container_id=attrs_dict["Id"]
                                     ))
        except Exception:
            print(traceback.format_exc())
            pass
        return data

    def restart_containers_server(self, name):
        if name:
            try:
                _container = self.client.containers.get(container_id=name)
            except Exception:
                print(traceback.format_exc())
                return False, "该容器名不存在"
            try:
                _container.restart()
            except Exception:
                print(traceback.format_exc())
                return False, "该容器重启失败"
            return True, None
        return False, "容器名不能为空"

    def get_conntainer_logs(self, name, tail=1000, **kwargs):
        """
        获取服务日志信息
        :param name: 容器id或容器名
        :param tail: 获取最后多少行数据
        :param kwargs: 以下参数可有可无
           1.timestamps -->bool-->是否显示时间戳
           2.until -->datetime-->获取该时间之前的日志数据
           3.since -->datetime-->获取该时间之后的日志数据
        :return:  (generator or str)
        """
        try:
            _container = self.client.containers.get(container_id=name)
        except Exception:
            print(traceback.format_exc())
            return "containers name or id is not exist".encode("utf-8")
        if kwargs:
            kwargs["tail"] = tail
            try:
                return _container.logs(**kwargs)
            except Exception:
                print(traceback.format_exc())
                return _container.logs(tail=tail)
        return _container.logs(tail=tail)


def download_target_patient_info(pacs_info,
                                 patient_info,
                                 queryRetrievel="SERIES",
                                 is_check=True):
    init = PACSUtil(**pacs_info)
    download_target = init.retrieval_some_image_new(patient_info, queryRetrievel=queryRetrievel)
    try:
        for i in download_target:
            if is_check:
                download_target.send("abort")
            else:
                print(i)
    except Exception:
        return i


def get_orthanc_uuid(study_instance_uid="********.1107.5.1.4.53621.30000014070423572131200000418"):
    if not study_instance_uid:
        return None
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/tools/find"
    data = {
        "Level": "SERIES",
        "Query": {
            "StudyInstanceUID": study_instance_uid
        },
        "Expand": True
    }
    try:
        response = requests.post(url=url, json=data,
                                 auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD),
                                 timeout=5)
        response_data = response.json()
        # print(response_data)
        series_dict = dict()
        for i in response_data:
            main_dicom_tags = i.get("MainDicomTags", "")
            instances = i.get("Instances", "")
            if main_dicom_tags and instances:
                seriesinstanceuid = main_dicom_tags.get("SeriesInstanceUID", "")
                SeriesDescription = main_dicom_tags.get("SeriesDescription", "")
                if seriesinstanceuid:
                    if SeriesDescription == "USC-UGuard CTP Summary":
                        status, uid = get_sop_one_by_series(series_instance_uid=seriesinstanceuid)
                        if status:
                            series_dict[seriesinstanceuid] = uid
                        else:
                            print("select sop instance number 1 error ")
                            series_dict[seriesinstanceuid] = instances[0]
                    else:
                        series_dict[seriesinstanceuid] = instances[0]
        return series_dict
    except Exception:
        log.error("failed to query series uid: {}".format(traceback.format_exc()))
        return None


def get_sop_one_by_series(series_instance_uid: str) -> (bool, str):
    """
    根据series查询sop第一张
    """
    if not series_instance_uid:
        return False, ""
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/tools/find"
    # url = F"http://{settings.ORTHANC_HOST}:8044/tools/find"
    data = {
        "Level": "Instance",
        "Query": {
            "SeriesInstanceUID": series_instance_uid
        },
        "Expand": True
    }
    try:
        response = requests.post(url=url, json=data,
                                 auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD),
                                 timeout=1)
        response_data = response.json()
        for i in response_data:
            main_dicom_tags = i.get("MainDicomTags", "")
            uid = i.get("ID", "")
            if main_dicom_tags and uid:
                InstanceNumber = main_dicom_tags.get("InstanceNumber", "")
                if InstanceNumber and InstanceNumber == "1":
                    return True, uid
    except Exception:
        print(traceback.format_exc())
        return False, ""
    return False, ""


def get_cpu_capacity():
    result_cpu = os.popen("df -h")
    text_cpu = result_cpu.readlines()
    p = ".+?([0-9,.]+[K,G,M,T]).*?([0-9,.]+[K,G,M,T]).*?([0-9,.]+[K,G,M,T]).*?([0-9,.]+)% */\n$"
    data = dict()
    for i in text_cpu:
        a = re.findall(p, i)
        if a:
            (data["total"], data["used"], data["remain"], data["used_percent"]) = a[0]
            break
    return data


def delete_orthanc_resources(study_list=['1.2.826.0.1.3680043.8.498.57984812852721919029640968557565776754',
                                         '1.2.826.0.1.3680043.8.498.66176375526529889984954756991736166047',
                                         '1.2.826.0.1.3680043.8.498.92752494184244317253187069348740586174',
                                         '1.2.826.0.1.3680043.8.498.52886077733002372631804053569897229626',
                                         '1.2.826.0.1.3680043.8.498.75388251741645924226299287933826849509']):
    # print(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD)
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/tools/find"
    data = {
        "Level": "STUDY",
        "Query": {
            "StudyInstanceUID": ""
        }

    }
    for i in study_list:
        data["Query"]["StudyInstanceUID"] = i
        response = requests.post(url=url, json=data,
                                 auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD),
                                 )
        response_data = response.json()
        if len(response_data) > 0:
            delete_url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/studies/{response_data[0]}"
            delete_response = requests.delete(url=delete_url,
                                              auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD))
            delete_response_data = delete_response.json()

    return True


def find_orthanc_resource_by_type(study, algorithm_type) -> list:
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/tools/find"
    data = {
        "Level": "Series",
        "Query": {
            "StudyInstanceUID": ""
        },
        "Expand": True

    }
    data["Query"]["StudyInstanceUID"] = study
    response = requests.post(url=url, json=data,
                             auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD),
                             )
    response_data = response.json()
    clear_series_list = []
    if len(response_data) > 0:
        for i in response_data:
            if i.get("MainDicomTags", None):
                MainDicomTags = i["MainDicomTags"]
                if MainDicomTags.get("SeriesDescription", None):
                    # print(">>>--->", i["MainDicomTags"]["SeriesDescription"])
                    if algorithm_type == "ctp":
                        if "CTP" in MainDicomTags["SeriesDescription"]:
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
                        elif "Perfusion Parameter Maps Colored" in MainDicomTags["SeriesDescription"]:
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
                        elif "AIF-VOF Location" in MainDicomTags["SeriesDescription"]:
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
                        # ctp提取cta去骨删除
                        elif "CTA" in MainDicomTags["SeriesDescription"]:
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
                    elif algorithm_type == "aspects":
                        if "ASPECTS" in MainDicomTags["SeriesDescription"]:
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
                        if "ICH" in MainDicomTags["SeriesDescription"]:  # 删除出血报告
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
                    elif algorithm_type == "cta":
                        if "CTA" in MainDicomTags["SeriesDescription"]:
                            if MainDicomTags.get("SeriesInstanceUID", None):
                                clear_series_list.append(MainDicomTags["SeriesInstanceUID"])
    return clear_series_list


def delete_orthanc_resources_by_series(series_list=['1.2.840.113619.2.340.3.168479077.740.1599528174.951',
                                                    '1.2.276.0.7230010.3.1.3.313264640.7.1620728547.999367',
                                                    '1.2.276.0.7230010.3.1.3.313264640.7.1620728548.999368',
                                                    '1.2.276.0.7230010.3.1.3.313264640.7.1620728549.999369']):
    print(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD)
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/tools/find"
    data = {
        "Level": "Series",
        "Query": {
            "SeriesInstanceUID": ""
        }

    }
    for i in series_list:
        data["Query"]["SeriesInstanceUID"] = i
        response = requests.post(url=url, json=data,
                                 auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD),
                                 )
        response_data = response.json()
        if len(response_data) > 0:
            delete_url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/series/{response_data[0]}"
            delete_response = requests.delete(url=delete_url,
                                              auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD))
            delete_response_data = delete_response.json()
            print(delete_response_data)
    return True


def request_api(url=None, json=None, auth=None, method="get", is_file=False):
    """

    :param url: 请求地址
    :param json: dict
    :param auth: tuple （username，password）
    :param method: 请求类型（get ， post， delete， put）
    :return: dict
    """
    obj = getattr(requests, method, None)
    try:
        response = obj(url=url,
                       json=json,
                       auth=auth)
        if is_file:
            return response.content
        return response.json()
    except Exception:
        print(traceback.format_exc())
        return dict()


def orthanc_c_find(is_debug=False,
                   patient_info={"PatientID": "",
                                 "current_index": 1,
                                 "per_page_count": 10},
                   querylevel="STUDY",
                   target_modality="orthanc80"):
    """
    使用ORTHANC做c_find查询目标设备的数据

    :param is_debug: 是否是调试
    :param patient_info: 病人查询条件
    :param querylevel: 查询级别
    :param target_modality:目标设备
    :return:带分页的数据： dict(has_next=False,
                       has_previous=False,
                       page_count=0,
                       record=[])
    """
    data_format = dict(has_next=False,
                       has_previous=False,
                       page_count=0,
                       record=[])
    # 获取查询条件转换接口
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/modalities/{target_modality}/query"

    query_data = {i: "" for i in settings.C_Find_Parameter_List}

    current_index = patient_info.pop("current_index", 1)

    per_page_count = patient_info.pop("per_page_count", 10)

    if patient_info:
        query_data.update(patient_info)
    """
           data = {
               "Level": "STUDY",
               "Query": {
                   "PatientID": "test.chenwensheng.19",
                   "PatientName": "*",
                   "NumberOfSeriesRelatedInstances": ""
               },
               "Normalize": False
           }
    """
    data = dict(Level=querylevel, Query=query_data, Normalize=False)

    response_orthanc_id = request_api(url=url,
                                      json=data,
                                      auth=(settings.ORTHANC_WADO_USERNAME,
                                            settings.ORTHANC_WADO_PASSWORD),
                                      method="post"
                                      )
    return_data = []
    if response_orthanc_id.get("ID", None):
        # 获取查询结果
        data_url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/queries/{response_orthanc_id['ID']}/answers?expand"

        response_data = request_api(url=data_url,
                                    auth=(settings.ORTHANC_WADO_USERNAME,
                                          settings.ORTHANC_WADO_PASSWORD))
        if not response_data:
            return False, data_format, "查询结果失败"

        for index, i in enumerate(response_data):
            data_dict = dict()
            data_dict["id"] = index
            for key, value in i.items():
                data_dict[value["Name"]] = value["Value"]
            return_data.append(data_dict)
        if is_debug:
            for i in return_data:
                print(i)
        page_count = math.ceil(len(return_data) / per_page_count) if len(return_data) / per_page_count > 1 else 1

        full_queryset = return_data[(current_index - 1) * per_page_count:current_index * per_page_count]

        data_format = dict(
            has_next=False if len(full_queryset) < per_page_count else True,
            has_previous=True if current_index > 1 else False,
            page_count=page_count,
            record=full_queryset
        )
        return True, data_format, None
    return False, data_format, "获取查询条件失败"


def orthanc_c_move(patient_info={"PatientID": ""},
                   querylevel="STUDY",
                   target_modality="orthanc80",
                   accept_aet="DockerOrthanc"):
    """
    使用ORTHANC做c_move查询目标设备的迁移数据到接收方aet

    :param patient_info: 病人查询条件
    :param querylevel: 查询级别
    :param target_modality:目标设备
    :param accept_aet:数据接收方aet
    :return:带分页的数据： dict(has_next=False,
                       has_previous=False,
                       page_count=0,
                       record=[])
    """
    # 获取查询条件转换接口
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/modalities/{target_modality}/query"

    query_data = {i: "" for i in settings.C_Find_Parameter_List}
    if patient_info:
        query_data.update(patient_info)
    """
       data = {
           "Level": "STUDY",
           "Query": {
               "PatientID": "test.chenwensheng.19",
               "PatientName": "*",
               "NumberOfSeriesRelatedInstances": ""
           },
           "Normalize": False
       }
    """
    data = dict(Level=querylevel, Query=query_data, Normalize=False)

    response_orthanc_id = request_api(url=url,
                                      json=data,
                                      auth=(settings.ORTHANC_WADO_USERNAME,
                                            settings.ORTHANC_WADO_PASSWORD),
                                      method="post"
                                      )
    if response_orthanc_id.get("ID", None):
        # 获取查询结果
        data_url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/queries/{response_orthanc_id['ID']}/retrieve"

        response_data = request_api(url=data_url,
                                    json=dict(TargetAet=accept_aet, Synchronous=False),
                                    auth=(settings.ORTHANC_WADO_USERNAME,
                                          settings.ORTHANC_WADO_PASSWORD),
                                    method="post"
                                    )
        if not response_data:
            return False, "转移失败"
        return True, response_data
    return False, "获取查询条件失败"


def get_orthanc_dicom_file(orthanc_instance_uid="20b0b5dc-ceec6102-c7a7db71-f3cb11b5-7b56bae4", seriesinstanceuid=None,
                           is_file=True):
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/instances/{orthanc_instance_uid}/file"
    br_content = request_api(url=url,
                             auth=(settings.ORTHANC_WADO_USERNAME,
                                   settings.ORTHANC_WADO_PASSWORD),
                             is_file=is_file)
    if br_content:
        SeriesInstanceUID_dir = os.path.join(settings.DOCKER_CTP_ROOT_DIR, seriesinstanceuid)
        if not os.path.exists(SeriesInstanceUID_dir):
            os.mkdir(SeriesInstanceUID_dir)
        file = os.path.join(SeriesInstanceUID_dir, orthanc_instance_uid)
        with open(file, 'wb') as f:
            f.write(br_content)


def wirte_config_file(config_path=None, wirte_data=None, key="DicomModalities"):
    """
     orthanc 配置文件写入
    :param config_path: orthanc.json 文件路径
    :param wirte_data: 要写入的值两种类型 list or str
    :param key: 对应orthanc.json 文件 key值
    :return: (status， error_info)
           status： 是否写入成功boolean
           error_info: 写入失败错误信息 str or None
    """
    data = ""
    with open(config_path, 'r') as f:
        for i in f:
            if not re.findall("^/.*|^\*.*", i.strip()):
                data += i
    if not data:
        return False, "orthanc.json file is blank"
    try:
        data_dict = json.loads(data)
    except Exception:
        return False, "json load error"
    if isinstance(wirte_data, list):
        data_dict[key][wirte_data[0]] = wirte_data[1]
    else:
        data_dict[key] = wirte_data
    data_tem = json.dumps(data_dict, ensure_ascii=False, indent=1).encode('utf-8')
    with open(config_path, 'wb+') as f:
        f.write(data_tem)
    return True, None


def orthanc_config_modify(config_path=None, **kwargs):
    """
    通过orthanc api接口更新内存中ae信息，以及通过写入orthanc.json文件方式实现持久化
    :param config_path: orthanc.json file path
    :param kwargs: dict(Host="name", AET="test","Port"=4242)
    :return:
    """
    vardata = kwargs
    modalitys = vardata.pop("alie_name", "")
    if not modalitys:
        return False
    modalitys = modalitys.replace("_", "")
    print(modalitys)
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/modalities/{modalitys}"
    data = vardata
    print(data)
    response_data = request_api(url=url,
                                json=data,
                                auth=(settings.ORTHANC_WADO_USERNAME,
                                      settings.ORTHANC_WADO_PASSWORD),
                                method="put"
                                )
    print(response_data)
    validate_url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/modalities?expand"
    response_validate_data = request_api(url=validate_url,
                                         auth=(settings.ORTHANC_WADO_USERNAME,
                                               settings.ORTHANC_WADO_PASSWORD)
                                         )
    if not response_validate_data.get(modalitys, None):
        return False, "orthanc配置设置失败"
    wirte_status, message = wirte_config_file(config_path=config_path,
                                              wirte_data=[modalitys,
                                                          [vardata["AET"],
                                                           vardata["Host"],
                                                           vardata["Port"]
                                                           ]
                                                          ])
    return wirte_status, message


def orthanc_modalities_c_move(target_aet, move_list, level="SERIES"):
    """
    使用orthanc内部执行c_move
    request body：
    {
        "Level": "SERIES",
        "Resources": [
            {
                "StudyInstanceUID": StudyInstanceUID,
                "SeriesInstanceUID": SeriesInstanceUID
            }
        ],
        "TargetAet": TargetAet,
        "Timeout": 60
    }
    :param target_aet: 目标aet
    :param move_list:需要移动的dicom 清单
    :param level：移动级别 （默认SERIES级别， 可支持STUDY， PATIENT，INSTANCE）
    :return:
    """
    url = F"http://{settings.ORTHANC_HOST}:{orthanc_port}/modalities/local/move"
    data = {
        "Level": level,
        "TargetAet": target_aet,
        "Timeout": 60
    }
    if not move_list:
        return False, "需要移动list为空"
    data["Resources"] = move_list
    response_data = request_api(url=url,
                                json=data,
                                auth=(settings.ORTHANC_WADO_USERNAME,
                                      settings.ORTHANC_WADO_PASSWORD),
                                method="post")
    log.info("c_move request url: {}, data: {}".format(url, data))
    log.info("c_move return message: {}".format(response_data))


class K8sMonitor:

    def __init__(self):
        """
        通过k8s配置获取k8s client
        """
        from kubernetes import client, config
        envir = os.environ
        k8s_config_path = envir.get("GE_CONFIG")
        # k8s_client = config.new_client_from_config('./config')  # 记载配置
        k8s_client = config.new_client_from_config(k8s_config_path)  # 记载配置

        k8s_host = envir.get("GE_HOST", "***********")
        self.k8s_ns = envir.get("GE_NAMESPACE", "uguard-stroke-v2")
        if k8s_host:
            # k8s_client.configuration.host = "https://***********:6443"
            k8s_client.configuration.host = k8s_host
        self.corev1api = client.CoreV1Api(api_client=k8s_client)

    def check_containers_server(self):
        """
        查询pod数据，符合docker的数据格式
        """
        data = []
        pod_list = self.corev1api.list_namespaced_pod(self.k8s_ns)
        for i in pod_list.items:
            metadata = i.metadata
            pod_name = metadata.labels.get("app")

            pod_id = metadata.name
            status = i.status
            phase = status.phase
            data.append({
                "container_name": pod_name,
                "container_id": pod_id,
                "name": settings.monitor_list.get(pod_name.replace("-", "_"), ""),
                "status": True if phase == "Running" else False,
                "status_error": status.message,
                "container_port": "",
                "restart_count": 0
            })
        return data

    def get_pod_logs(self, pod_id='webapi-v1-75d664849-ns7xg', tail_lines=2000):
        """
        根据pod id 查询日志
        """
        log_content = self.corev1api.read_namespaced_pod_log(
            pod_id,
            self.k8s_ns,
            pretty=True,
            tail_lines=tail_lines,
        )
        return log_content

if __name__ == '__main__':
    # orthanc_modalities_c_move(target_aet="OrthancDEMO", move_list=[{
    #     "StudyInstanceUID": "1.2.840.113619.2.428.3.3389708238.354.1581121633.186",
    #     "SeriesInstanceUID": "1.2.276.0.7230010.3.1.3.313264896.7.1618193757.6620"
    # },
    #     {
    #         "StudyInstanceUID": "1.2.840.113619.2.428.3.3389708238.354.1581121633.186",
    #         "SeriesInstanceUID": "1.2.276.0.7230010.3.1.3.313264896.7.1618193755.6618"
    #     }
    # ])

    find_orthanc_resource_by_type("1.2.840.113619.2.340.3.168479077.740.1599528174.879", "ctp")

    # delete_orthanc_resources_by_series()
    # vardata = dict(alie_name="target_moda",
    #                AET="songtesttest",
    #                Host="************",
    #                Port=4262,
    #                config_path="D:\\code\\upixel-station-backend\\server\\async\\tls\\orthanc.json")
    # a = orthanc_config_modify(**vardata)
    # print(a)
    # get_orthanc_dicom_file()
    # a = orthanc_c_find()
    # print(a)
    # tls_config = docker.docker-server-tls.TLSConfig(
    #     client_cert=('/path/to/client-cert.pem', '/path/to/client-key.pem')
    # )
    # client = docker.DockerClient(base_url='<https_url>', docker-server-tls=tls_config)
    # a = DockerMonitor()
    # c = a.check_containers_server()
    # print(c)

    # a, info = download_target_patient_info(
    #     pacs_info={
    #         "target_ip": "***********",
    #         "target_ae": "DockerOrthanc",
    #         "target_port": 4242},
    #     patient_info=dict(
    #         SeriesInstanceUID='********.1107.5.1.4.53621.30000014070423244203100005720'),
    #     queryRetrievel="SERIES",
    #     is_check=True)
    #
    # print(a, "===", info)
    # get_orthanc_uuid()
    # delete_orthanc_resources()
    # orthanc_config_modify()
