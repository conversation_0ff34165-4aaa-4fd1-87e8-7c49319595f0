import os
import json
import pydicom
import requests
import threading

_env = os.environ
GE_CALLBACK_HOST = _env.get('GE_CALLBACK_HOST', 'edison-ecm-service.default')
GE_CALLBACK_PORT = _env.get('GE_CALLBACK_PORT', '8020')
GE_CALLBACK_START = _env.get('GE_CALLBACK_START', 0)
LOCAL_WEB_SERVER_HOST = _env.get('LOCAL_WEB_SERVER_HOST', "***********")
LOCAL_WEB_SERVER_PORT = _env.get('LOCAL_WEB_SERVER_PORT', "4224")
LOCAL_API_SERVER_PORT = _env.get('UPIXEL_PORT', "4223")
GE_CALLBACK_THIRD_PARTY_ID = _env.get('GE_CALLBACK_THIRD_PARTY_ID', 2)
aspect_file_dir = "/data/ctpdata/dcm"
# aspect_file_dir = r"C:\Users\<USER>\Desktop\07280000"

C_Find_Parameter_List = [
    "PatientName",
    "PatientID",
    "PatientSex",
    "ProtocolName",
    "StudyInstanceUID",
    "SeriesInstanceUID",
    "PatientAge",
    "PatientWeight",
    "PatientBirthDate",
    "StudyDescription",
    "StudyDate",
    "StudyTime",
    "StudyID",
    "SeriesDescription",
    "SeriesDate",
    "SeriesTime",
    "SeriesNumber",
    "InstitutionName",
    "SOPInstanceUID",
    "NumberOfFrames",
    "SliceThickness",
    "PixelSpacing",
    "Rows",
    "Columns",
    "SliceLocation",
    "Modality",
    "AcquisitionType",
    "StationName",
    "InstanceNumber",
    "BodyPartExamined"
]


def dataset_to_dict(identifier):
    """
    dicom 文件对象转换为dicom 字典
    :param identifier: dicom 读取文件对象 object
    :return: dicom 字典 dict
    """
    # print(identifier)
    pacs_content = dict()
    for key in C_Find_Parameter_List:
        if key == "PatientName":
            patient_name = identifier.get('PatientName', '')
            if patient_name:
                # print(patient_name.original_string)
                if not patient_name.original_string:
                    pacs_content["PatientName"] = ""
                else:
                    if isinstance(patient_name.original_string, str):
                        pacs_content['PatientName'] = patient_name.original_string
                        # print(pacs_content["PatientName"])
                    if isinstance(patient_name.original_string, bytes):
                        try:
                            pacs_content['PatientName'] = bytes.decode(patient_name.original_string)
                        except Exception:
                            try:
                                pacs_content['PatientName'] = (patient_name.original_string).decode('gbk')
                            except Exception:
                                return dict()
        elif key == "PixelSpacing":
            pixel_spacing_tag = identifier.get('PixelSpacing', '')
            if pixel_spacing_tag:
                pacs_content['PixelSpacing'] = "/".join(
                    [str(pixel) for pixel in pixel_spacing_tag])
        else:
            pacs_content[key] = identifier.get(key, '')
    return pacs_content


def read_dcm_file(series_instance_uid):
    """
          从文件目录读取dicom数据
    :param series_instance_uid: series_instance_uid
    :return: (status, data, pixel_spacing, slice_thickness, dataset_dict)
            status:下载数据是否成功 boolean
            series_info_list：dicom数据列表 （list） ：
                            列表每一项：dict(index=InstanceNumber,img=sitk.GetArrayFromImage(image).tolist())
            pixel_spacing: 像素间隔：list [0.5, 0.5]
            slice_thickness:单帧厚度 float 0.5
            dataset_dict：病人信息字典dict

    """

    dcm_file_path = os.path.join(aspect_file_dir, str(series_instance_uid))
    data = []
    try:
        for root, dir, files in os.walk(dcm_file_path):
            if not files:
                return False, None
            for file in files:
                file_path = os.path.join(dcm_file_path, file)
                ds = pydicom.read_file(file_path)
                if ds:
                    data.append(dataset_to_dict(ds))
    except Exception as e:
        print("read dcm file error ", e)
        return False, None
    return True, data


def callback_request_GE(body, status):
    """
    {
      "processingStatus": "1",
      "seriesUid": "1.3.12.2.1107.5.12.7.2361.30000014123100425632800000037"
    }

    参数说明：
        processingStatus:序列处理状态[0:处理中, 1:处理完成, 2:处理失败]
        seriesUid:序列Uid

    Response body

    {
      "status": "200",
      "data": 1
        }
    """
    body = json.loads(body)
    print("准备回调GE接口，算法处理中", body)
    if isinstance(body, dict):
        print("开始时请求GE的/third-party/storageData接口[0:处理中, 1:处 理完成, 2:处理失败],", status)
        SeriesInstanceUID = body.get('SeriesInstanceUID', '')
        # 算法类型
        algorithm_type = body.get("algorithm_type", "")
        ds = body.get("tags", {})
        patientId = ds.get("PatientID", "")
        studyId = ds.get("StudyID", "")
        studyUid = ds.get("StudyInstanceUID", "")
        patientName = ds.get("PatientName", "")
        patientAge = ds.get("PatientAge", "")
        patientSex = ds.get("PatientSex", "")
        StudyTime = ds.get("StudyTime", "")
        StudyDescription = ds.get("StudyDescription", "")
        SeriesDescription = ds.get("SeriesDescription", "")
        Modality = ds.get("Modality", "")
        SeriesNumber = ds.get("SeriesNumber", "")
        SeriesCount = ds.get("SeriesCount", "")
        BodyPartExamined = ds.get("BodyPartExamined", "")
        requestUrl = ""
        if status == "1":
            requestUrl = "http://" + LOCAL_WEB_SERVER_HOST + ":" + LOCAL_WEB_SERVER_PORT + "/read/" + studyUid
        callbackUrl = "http://" + LOCAL_WEB_SERVER_HOST + ":" + LOCAL_API_SERVER_PORT + "/deleteStorageDicom"
        if SeriesInstanceUID and status:
            data = {
                "patientId": patientId,
                "patientName": patientName,
                "patientAge": patientAge,
                "patientSex": patientSex,
                "callbackUrl": callbackUrl,
                "studyList": [
                    {"patientId": patientId,  # 患者Id,必填项
                     "studyId": studyId,  # 检查Id,必填项
                     "studyUid": studyUid,  # 检查Uid
                     "bodypartExamined": BodyPartExamined,  # 检查部位
                     "modalityName": Modality,  # 设备名字
                     "studyDescription": StudyDescription,  # 检查描述
                     "studyTime": StudyTime,  # 检查时间
                     "seriesList": [
                         {"processingStatus": status,  # 处理状态,必填项[0:处理中, 1:处 理完成, 2:处理失败]
                          "requestUrl": requestUrl,  # 跳转url(不能有安全认证)
                          "seriesCount": SeriesCount,  # 列图像数量
                          "seriesDescription": SeriesDescription,  # 序列描述
                          "seriesNumber": SeriesNumber,  # 学列号
                          "seriesUid": SeriesInstanceUID,  # 序列Uid
                          "studyId": studyId,  # 检查id,必填项
                          "aiModel": algorithm_type,
                          "thirdConfigId": GE_CALLBACK_THIRD_PARTY_ID  # 第三方AI公司标识,必填项(不同AI公司id不一样, 想确认id和GE工作人员联系)
                          }]}],
            }
            url = "http://" + GE_CALLBACK_HOST + ":" + GE_CALLBACK_PORT + "/third-party/storageData"
            headers = {"Content-Type": "application/json"}
            try:
                response = requests.post(url, json=data, headers=headers)
                print("GE response：", response)
            except Exception as e:
                print("GE请求处理中报错：", e)


def begin_request_GE(body, status):
    print("是否开启GE回调", GE_CALLBACK_START)
    if GE_CALLBACK_START:
        thread_ge = threading.Thread(target=callback_request_GE, args=(json.dumps(body), status))
        thread_ge.start()


def check_ge_callback(status, text, seriesinstanceuid):
    push_task = ["aspects", "ctp"]
    if status:
        if text not in push_task:
            read_status,ds=read_dcm_file(seriesinstanceuid)
            if read_status:
                begin_request_GE(ds,3)
    else:
        read_status, ds = read_dcm_file(seriesinstanceuid)
        if read_status:
            begin_request_GE(ds, 3)

