#!/usr/bin/env python
import os
import sys
import django

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
django.setup()


def main():
    argv = [sys.argv[0], 'runserver', '0.0.0.0:4201']
    if len(sys.argv) > 1:
        argv = sys.argv

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
    from django.core.management import execute_from_command_line
    execute_from_command_line(argv)


if __name__ == "__main__":
    main()
