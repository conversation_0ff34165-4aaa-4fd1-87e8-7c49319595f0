#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : views
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/19 11:23
"""
import json
import logging
import datetime

from django.db.models import F
# Create your views here.
from django.http import JsonResponse

from server.common.base import check_auth_code
from server.common.code import RetCode, Const
from server.common.mongoConnector import MongoDB
from server.common.remote_api import RabbitMQProducer
from server.common.views import BaseView
from server.study.models import Study
from server.study.service import StudyCallbackService, StudySeriesService, StudyRecalculateService, StudyService, \
    DeleteViewTools, StudyReportService

log = logging.getLogger("django")


class StudyView(BaseView):
    """检查视图"""

    def get(self, request):
        """
        检查列表查询

        :param request:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        params = request.GET
        log.info("Study[list] > params:{}".format(params))
        ret_code, data = StudyService.list(params)
        return self.of(ret_code, data=data)

    def put(self, request):
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        body = request.PUT
        studyUid = body.get("studyUid")
        is_confirmed = body.get("is_confirmed")
        if not studyUid:
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        study_qs = Study.objects.filter(study_instance_uid=studyUid)
        if not study_qs.exists():
            return self.of(RetCode.STUDY_NOT_FOUND)
        study_qs.update(is_confirmed=is_confirmed, gmt_modified=F("gmt_modified"))
        return self.of(RetCode.OK)


class StudyCallbackView(BaseView):
    """检查回调重构"""

    def post(self, request):
        """
        检查回调

        :param request:
        :return:
        """

        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", Const.DEFAULT_HOSPITAL_UID)
        hospital_name = request_body.get("hospitalName", "")
        study_orthanc_id = request_body.get("studyId", None)
        tags = request_body.get("tags", None)
        if not study_orthanc_id:
            return self.of(RetCode.STUDY_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.STUDY_TAGS_IS_EMPTY)
        study_instance_uid = tags.get("StudyInstanceUID", "")
        if not study_instance_uid:
            return self.of(RetCode.STUDY_INSTANCE_UID_IS_EMPTY)
        log.info("Study[{}] > start callback".format(study_instance_uid))
        callback_service = StudyCallbackService(study_instance_uid, study_orthanc_id, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        log.info("Study[callback] > response: {}".format(ret_code.msg))
        return self.of(ret_code)


class StudySeriesView(BaseView):

    def get(self, request, study_instance_uid):
        """
        获取检查下序列

        :param request:
        :param study_instance_uid: 检查UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Study[series] > studyInstanceUID: {}, get series".format(study_instance_uid))
        ret_code, data = StudySeriesService.get_series(study_instance_uid)
        return self.of(ret_code, data=data)


class StudySeriesV2(BaseView):

    def get(self, request, study_instance_uid):
        """
        获取检查下序列

        :param request:
        :param study_instance_uid: 检查UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Study[series v2] > studyInstanceUID: {}, get series".format(study_instance_uid))
        ret_code, data = StudySeriesService.get_series_v2(study_instance_uid)
        return self.of(ret_code, data=data)


class StudyRecalculateView(BaseView):

    def post(self, request, study_instance_uid):
        """
        重新计算

        :param request:
        :param study_instance_uid: 检查UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        log.info("Study[recalculate] > studyInstanceUID: {}, body:{}".format(
            study_instance_uid, request_body))
        ret_code = StudyRecalculateService.recalculate(study_instance_uid, request_body)
        return self.of(ret_code)


class StudyReportView(BaseView):
    def post(self, request, study_id):
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        log.info("Study[report] > studyId: {}, body:{}".format(study_id, request_body))
        ret_code, data = StudyReportService.get_report(study_id, request_body)
        return self.of(ret_code, data=data)


class StudyErrorMsgById(BaseView):

    def get(self, request, study_id):
        """
        根据study主键id查询序列的异常信息

        :param request:
        :param study_id: 检查主键id
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Study[errorCode] > studyId: {}".format(study_id))
        data = StudyService.error_list(study_id)
        return self.of(RetCode.OK, data=data)


class DeleteStudyView(BaseView):
    def get(self, request):
        """重构删除接口"""

        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.GET
        period = request_body.get("period", "")

        start_date, end_date = period.split("-")
        start_date = datetime.datetime.strptime(start_date, "%Y%m%d")
        end_date = datetime.datetime.strptime(end_date, "%Y%m%d")
        study_qs = Study.objects.filter(gmt_modified__date__range=(start_date, end_date)).count()
        return self.of(RetCode.OK, data={"count": study_qs})

    def post(self, request):
        """重构删除接口"""

        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        study_list = request_body.get("studyList", [])
        period = request_body.get("period", "")
        delete_study_id = DeleteViewTools.create_delete_data(study_list, period)
        if delete_study_id == -1:
            message = "study not exists"
            return self.fail(400, message=message)
        if delete_study_id is None:
            message = "params can not empty"
            return self.fail(400, message=message)
        # 组装消息
        message = dict(studyList=study_list, period=period, delete_study_id=delete_study_id)
        queue_name = "study_delete_task"
        try:
            RabbitMQProducer.simple_send(queue_name, message)
            log.info(f"Study[delete] > studyList:{study_list} queue_name: {queue_name}, message:{message}")
            response["message"] = "删除成功"
            response["code"] = 200
            response["status"] = True
        except:
            response["message"] = "delete study error"
            response["code"] = 400
            response["status"] = False
        return JsonResponse(response)


class StudyScoreView(BaseView):
    def calculate_totals(self, score_data):
        # 计算其他评分总和
        if 'sebes_score' in score_data and 'value' in score_data['sebes_score']:
            score_data['sebes_score_total'] = sum(score_data['sebes_score']['value'])
        if 'greab_score' in score_data and 'value' in score_data['greab_score']:
            score_data['greab_score_total'] = sum(score_data['greab_score']['value'])
            
        # 计算血管狭窄率
        if 'vessel_narrowing' in score_data:
            for series in score_data['vessel_narrowing']:
                for vessel in series['vessels']:
                    # 如果是最新记录，则计算血管狭窄率
                    if series['is_latest']:
                        narrowing_point = vessel.get('narrowing_point', {}).get('value', 0)
                        distal_point = vessel.get('narrowing_distal_point', {}).get('value', 1)
                        if distal_point != 0:
                            vessel['narrowing_rate'] = round((1 - narrowing_point / distal_point) * 100, 2)
                        else:
                            vessel['narrowing_rate'] = 0
                    
        return score_data

    def get_score_text(self, score_type, value):
        mongodb = MongoDB()
        score_text = mongodb.query_one(None, 'score_text', None)
        
        if not score_text:
            return ""
        try:
            if score_type == 'mfs_score':
                mfs_options = score_text.get('mfs', {}).get('options', [])
                for option in mfs_options:
                    if option['score'] == value:
                        return option['des']
            elif score_type == 'greab_score':
                greab_items = score_text.get('greab', {}).get('items', [])
                text = ""
                for i, item in enumerate(greab_items):
                    options = item.get('options', [])
                    location = item.get('location', "")
                    for option in options:
                        if option['score'] == value[i]:
                            des_text = option.get('des', "").split("：")[1]
                            text += f"{location}：{des_text}\n"
                return text.strip()
        except Exception as e:
            log.error(f"获取评分文案时发生错误: {str(e)}", exc_info=True)

        return ""

    def get(self, request, study_instance_uid):
        """查询评分"""
        score_data = MongoDB().query_one(study_instance_uid, 'score', 'study_instance_uid')
        
        # 默认评分值
        default_response = {
            "mfs_score": 0,
            "mfs_score_text": "用于反映蛛网膜下隙积血程度与脑血管痉挛之间的关系，级别越高，并发脑管痉挛的概率越大。",
            "greab_score": [1, 1, 1, 1],
            "greab_score_total": 0,
            "greab_score_text": "又称Graeb脑室内出血评分，主要用于CT影像学上对脑室内出血量进行测评。",
            "sebes_score": [0, 0, 0, 0],
            "sebes_score_total": 0,
            "sebes_score_text": "是一种基于计算机断层扫描（CT）的脑水肿半定量评分方法，用于评估蛛网膜下腔出血（SAH）患者的脑水肿情况。",
            "is_vasospasm": False,
            "vessel_narrowing": []
        }

        if score_data:
            # score_data = self.calculate_totals(score_data)
            response_data = {
                "mfs_score": score_data.get('mfs_score', {}).get('value', default_response["mfs_score"]),
                "mfs_score_text": (self.get_score_text('mfs_score', score_data.get('mfs_score', {}).get('value')) 
                    if 'mfs_score' in score_data 
                    else default_response["mfs_score_text"]),
                "greab_score": score_data.get('greab_score', {}).get('value', default_response["greab_score"]),
                "greab_score_total": score_data.get('greab_score_total', default_response["greab_score_total"]),
                "greab_score_text": (self.get_score_text('greab_score', score_data.get('greab_score', {}).get('value')) 
                    if 'greab_score' in score_data 
                    else default_response["greab_score_text"]),
                "sebes_score": score_data.get('sebes_score', {}).get('value', default_response["sebes_score"]),
                "sebes_score_total": score_data.get('sebes_score_total', default_response["sebes_score_total"]),
                "sebes_score_text": (self.get_score_text('sebes_score', score_data.get('sebes_score', {}).get('value')) 
                    if 'sebes_score' in score_data 
                    else default_response["sebes_score_text"]),
                "is_vasospasm": score_data.get('is_vasospasm', {}).get('value', False),
                "vessel_narrowing": score_data.get('vessel_narrowing', []) or []
            }
            return self.ok(data=response_data, message="成功")
        
        return self.ok(data=default_response, message="未找到相关评分数据，返回默认值")

    def put(self, request, study_instance_uid):
        """修改评分"""
        try:
            data = request.PUT
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            mongodb = MongoDB()
            existing_record = mongodb.query_one(study_instance_uid, 'score', 'study_instance_uid')
            
            update_data = {}
            
            # 处理常规评分数据
            for key in ['mfs_score', 'greab_score', 'sebes_score', 'is_vasospasm']:
                if key in data:
                    update_data[key] = data[key]
                    update_data[key]['update_time'] = current_time

            # 处理血管狭窄率数据
            if 'vessel_narrowing' in data:
                series_data = data['vessel_narrowing']
                if existing_record and 'vessel_narrowing' in existing_record:
                    # 更新现有记录中的序列is_latest标志
                    for series in existing_record['vessel_narrowing']:
                        series['is_latest'] = False
                        # 将所有血管的is_latest设置为False
                        if 'vessels' in series:
                            for vessel in series['vessels']:
                                vessel['is_latest'] = False
                    
                    # 检查是否存在相同序列的记录
                    series_instance_uid = series_data.get('series_instance_uid')
                    series_exists = False
                    
                    for series in existing_record['vessel_narrowing']:
                        if series['series_instance_uid'] == series_instance_uid:
                            # 获取新的血管数据（此时只包含一个血管的数据）
                            new_vessel = series_data.get('vessels', [])[0] if series_data.get('vessels') else None
                            if not new_vessel:
                                continue
                            
                            existing_vessels = series.get('vessels', [])
                            vessel_name = new_vessel.get('vessel_name')
                            
                            # 设置新血管数据的is_latest为True
                            new_vessel['is_latest'] = True
                            new_vessel['update_time'] = current_time
                            
                            # 查找并更新特定血管的数据
                            vessel_found = False
                            for i, existing_vessel in enumerate(existing_vessels):
                                if existing_vessel.get('vessel_name') == vessel_name:
                                    # 更新现有血管数据
                                    existing_vessels[i] = new_vessel
                                    vessel_found = True
                                    break
                                    
                            # 如果是新的血管，则添加到列表中
                            if not vessel_found:
                                existing_vessels.append(new_vessel)
                            
                            # 更新序列信息
                            series['vessels'] = existing_vessels
                            series['update_time'] = current_time
                            series['is_latest'] = True
                            series_exists = True
                            break
                    
                    if not series_exists:
                        # 添加新的序列记录
                        series_data['is_latest'] = True
                        series_data['create_time'] = current_time
                        series_data['update_time'] = current_time
                        # 确保新序列中的血管数据有is_latest标记
                        for vessel in series_data.get('vessels', []):
                            vessel['is_latest'] = True
                            vessel['update_time'] = current_time
                        existing_record['vessel_narrowing'].append(series_data)
                    
                    update_data['vessel_narrowing'] = existing_record['vessel_narrowing']
                else:
                    # 创建新的血管狭窄率记录
                    series_data['is_latest'] = True
                    series_data['create_time'] = current_time
                    series_data['update_time'] = current_time
                    # 确保新序列中的血管数据有is_latest标记
                    for vessel in series_data.get('vessels', []):
                        vessel['is_latest'] = True
                        vessel['update_time'] = current_time
                    update_data['vessel_narrowing'] = [series_data]

            update_data = self.calculate_totals(update_data)
            
            if existing_record:
                # 确保 update_data 不为空
                if not update_data:
                    return self.ok(status=False, message="没有需要更新的数据")
                    
                result = mongodb.update(
                    update_data,
                    'score',
                    {'study_instance_uid': study_instance_uid}
                )
                success = result.modified_count > 0
            else:
                # 创建新记录
                if not update_data:
                    return self.ok(status=False, message="没有需要创建的数据")
                    
                update_data['study_instance_uid'] = study_instance_uid
                update_data['create_time'] = current_time
                result = mongodb.create(update_data, 'score')
                success = result.inserted_id is not None
            
            if success:
                return self.ok(message="成功更新评分数据")
            else:
                return self.ok(status=False, message="更新或插入失败")
                
        except Exception as e:
            log.error(f"更新评分数据时发生错误: {str(e)}", exc_info=True)
            return self.ok(status=False, message=f"更新评分数据时发生错误")


class ScoreTextView(BaseView):
    def get(self, request):
        """获取评分文案"""
        try:
            mongodb = MongoDB()
            score_text = mongodb.query_one(None, 'score_text', None)
            if score_text:  # 获取查询结果的第一个文档
                return self.ok(data=score_text, message="成功获取评分文案")
            else:
                return self.ok(status=False, message="未找到评分文案数据")
        except Exception as e:
            return self.ok(status=False, message=f"获取评分文案时发生错误")
        

class WaterUptakeRateView(BaseView):
    def get(self, request, study_instance_uid):
        """获取水摄取率
        
        Args:
            request: HTTP请求对象
            study_instance_uid: 检查UID
            
        Returns:
            JsonResponse: 包含最近修改的序列水摄取率信息
        """
        try:
            # 验证用户权限
            creator = check_auth_code(request)
            if not creator:
                return self.of(RetCode.UNAUTHORIZED)

            # 查询该检查下最近修改的水摄取率记录
            mongodb = MongoDB()
            water_uptake_rate = mongodb.query_one_new(
                study_instance_uid,
                'water_uptake_rate',
                'study_instance_uid',
                sort=[('update_time', -1)],  # 按更新时间降序排序
                projection={
                    'infarction_area': 0,
                    'non_infarction_area': 0
                }  # 排除不需要的字段
            )

            if not water_uptake_rate:
                return self.ok(
                    status=False,
                    message="未找到水摄取率数据",
                    data=None
                )

            return self.ok(
                message="成功获取水摄取率数据",
                data=water_uptake_rate
            )

        except Exception as e:
            log.error(f"获取水摄取率数据时发生错误: {str(e)}", exc_info=True)
            return self.ok(status=False, message=f"获取水摄取率数据时发生错误")
