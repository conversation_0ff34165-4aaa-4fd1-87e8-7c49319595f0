#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/19 11:30
"""
import datetime
import json
import logging
import os
import shutil
import threading
import time
import traceback
import uuid

from bson import ObjectId
from django import db
from django.db.models import Q

from server import settings
from server.algorithm.models import AlgorithmTask, AlgorithmResult
from server.image.models import CallBackDICOM
from server.common.code import (
    RetCode,
    Const,
    AlgorithmErrorCodeMap, Config,
)
from server.common.mongoConnector import MongoDB
from server.common.remote_api import RabbitMQProducer, OrthancApi
from server.image.service import ImageCallbackService
from server.report.models import PDFReport
from server.series.models import Series, FeatureMap, SeriesAlgorithm
from server.series.service import SeriesDownloader, SeriesThumbnail, SeriesSplitter
from server.study.models import Study, DeleteStudy
from server.systemconfig.models import SystemConfig
from server.systemconfig.system_config_utils import SystemConfigUtils
from server.ge.ge_api import (
    ge_apply_calc_schedule_notice,
    validate_dicom_failed,
    storage_data
)
from server.user.auth import AuthVerify

log = logging.getLogger("django")


class StudyService:
    @staticmethod
    def list(params):
        begin_time = time.time()
        page_number = int(params.get("pageNumber", "1"))
        page_size = int(params.get("pageSize", "12"))
        if page_size > 100:
            log.info("Study[list] > pageSize is too large")
            return RetCode.LIST_PAGE_SIZE_IS_TOO_LARGE, None
        study_queryset = Study.objects.all()
        patient_info = params.get("patient", None)
        if patient_info:
            study_queryset = study_queryset.filter(Q(patient_id__icontains=patient_info) |
                                                   Q(patient_name__icontains=patient_info))
        start_date = params.get("startDate", None)
        end_date = params.get("endDate", None)
        if start_date and end_date:
            start_time = datetime.datetime.strptime(start_date, '%Y%m%d').strftime("%Y-%m-%d %H:%M:%S")
            end_time = (datetime.datetime.strptime(end_date, '%Y%m%d') +
                        datetime.timedelta(hours=23, minutes=59, seconds=59)).strftime("%Y-%m-%d %H:%M:%S")
            study_queryset = study_queryset.filter(gmt_modified__gt=start_time, gmt_modified__lt=end_time)
        count_time = time.time()
        total_elements = study_queryset.count()
        log.debug("Study[list] > count {} studies in {:.5}s".format(total_elements, (time.time() - count_time)))
        data = dict(pageNumber=page_number, pageSize=page_size, totalPages=0, totalElements=total_elements, record=[])
        if total_elements <= 0:
            log.info("Study[list] > study not found, totalRecords: %s", total_elements)
            return RetCode.OK, data
        total_pages = int((total_elements - 1) / page_size + 1)
        data["totalPages"] = total_pages
        if page_number > total_pages:
            log.info("Study[list] > invalid pageNumber: %s, totalPages: %s", page_number, total_pages)
            return RetCode.OK, data
        start_index = (page_number - 1) * page_size
        end_index = page_size * page_number
        list_time = time.time()
        study_list_queryset = study_queryset.order_by('-gmt_modified')[start_index:end_index]
        log.debug("Study[list] > query {} studies in {:.5}s".format(
            len(study_list_queryset), (time.time() - list_time)))
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        record_list = []
        origin_type_list = [Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA,
                            Const.ALGORITHM_TYPE_OTHER, ""]
        for study in study_list_queryset:
            record = study.to_dict()
            # 增加确认信息
            record["is_confirmed"] = study.is_confirmed
            # 收集序列信息
            other_series_count = 0
            series_queryset = study.series_set.filter(type__in=origin_type_list).order_by("type", "series_description")
            series_description = set()
            downloaded = 1
            series_uid_list = []
            geapi_status_list = []
            for series in series_queryset:
                if series.series_description:
                    series_description.add(series.series_description)
                if series.type == Const.ALGORITHM_TYPE_OTHER:
                    other_series_count += 1
                if not series.downloaded:
                    downloaded = 0
                series_uid_list.append(series.series_instance_uid)
                geapi_status_list.append(series.geapi_status)
            if not record["studyDescription"]:
                record["studyDescription"] = ",".join(series_description)
            # record["modality"] = ",".join(set(s.mod`ality for s in series_list))
            # 收集算法处理状态
            # downloaded = 0 if any(not s.downloaded for s in original_series_list) else 1
            if GE_CALLBACK_START == '1':
                geApiStatus = None
                if 2 in geapi_status_list:
                    geApiStatus = 2
                elif 3 in geapi_status_list:
                    geApiStatus = 3
                elif 1 in geapi_status_list:
                    geApiStatus = 1

                record["geApiStatus"] = geApiStatus
            record["downloadStatus"] = downloaded
            record["finishStatus"] = 0
            task_queryset = AlgorithmTask.objects.filter(series_uid__in=series_uid_list).order_by("algorithm_type")
            # 算法计算进度
            success_list = []
            failed_count = 0
            for task in task_queryset:
                algorithm_type = task.algorithm_type
                finish_percent = task.finish_percent
                if finish_percent is None:
                    continue
                if finish_percent < 100:
                    record["percent"] = {"type": algorithm_type, "value": finish_percent}
                    break
                elif finish_percent == 100 and algorithm_type.upper() not in success_list:
                    success_list.append(algorithm_type.upper())
                elif finish_percent > 100:
                    failed_count += 1
            if "percent" not in record and downloaded:
                if len(success_list) > 0:
                    success_list.sort()
                    if success_list[0] == Const.ALGORITHM_TYPE_ASPECTS.upper():
                        success_list[0] = "NCCT"
                    record["finishAlgorithms"] = ",".join(success_list)
                record["finishStatus"] = 1 if (failed_count == 0 and other_series_count == 0) else (
                    3 if (len(success_list) > 0 and (failed_count > 0 or other_series_count > 0)) else 2
                )
            if "percent" not in record:
                record["percent"] = {}
            record_list.append(record)
        data["record"] = record_list
        log.debug("Study[list] > query in {:.5}s".format((time.time() - begin_time)))
        return RetCode.OK, data

    @classmethod
    def error_list(cls, study_id):
        """
        根据study主键id获取异常信息list
        """
        series_uid_list = list(Series.objects.filter(
            study_id=study_id
        ).values_list(
            'series_instance_uid',
            flat=True
        ).distinct())
        algorith_task = AlgorithmTask.objects.filter(
            series_uid__in=series_uid_list,
            error_code__isnull=False,
        ).exclude(
            error_code=0
        )
        # 获取code和msg映射组成字典列表
        series_qs = Series.objects.filter(
            study_id=study_id,
            type='other'
        )
        # 确定type只有other的情况，且没有算法任务
        if series_qs.exists():
            error_code = 30000
            # 进一步区分层厚提示
            series = series_qs.first()
            if series.slice_thickness:
                try:
                    slice_thickness = float(series.slice_thickness)
                    aspect_slice_thickness_min = int(SystemConfigUtils().getConfigValue(Config.ASPECT_SLICE_THICKNESS_MIN, 2))
                    aspect_slice_thickness_max = int(SystemConfigUtils().getConfigValue(Config.ASPECT_SLICE_THICKNESS_MAX, 6))
                    if not aspect_slice_thickness_min < slice_thickness <= aspect_slice_thickness_max:
                        error_code = 30001
                except Exception:
                    log.warning("invalid slice thickness: {}".format(series.slice_thickness))
            return [{"errorCode": error_code, "errorMsg": AlgorithmErrorCodeMap.get(error_code, '')}]
        error_code_list = algorith_task.values_list(
            "error_code",
            flat=True
        ).order_by().distinct()
        result = map(
            lambda item: {
                "errorCode": item,
                "errorMsg": AlgorithmErrorCodeMap.get(item, "")
            },
            error_code_list
        )

        return list(result)


class StudyPushHandler:
    def __init__(self, study_id, ge_callback):
        self.study_id = study_id
        self.ge_callback = ge_callback

    def start(self):
        study = Study.objects.get(id=self.study_id)
        study_instance_uid = study.study_instance_uid
        series_qs = Series.objects.filter(study__id=study.id, type=Const.ALGORITHM_TYPE_CTP)
        if not series_qs.exists():
            log.info("Study[{}] > toshiba ctp series not found".format(study_instance_uid))
            return
        notify_data = dict(action="update",
                           data=dict(StudyInstanceUID=study_instance_uid, downloadStatus=1, finishStatus=-1))
        series_instance_uids = list()
        # 下载原图
        for series in series_qs:
            series_instance_uid = series.series_instance_uid
            series_instance_uids.append(series_instance_uid)
            if series.downloaded:
                continue
            downloader = SeriesDownloader(study_instance_uid, series_instance_uid, series.orthanc_id, True)
            if not downloader.run():
                RabbitMQProducer.ws_notify("ws.notify.study_percent", notify_data)
                log.info("Study[{}] > series:{}, failed to download dicom".format(
                    study_instance_uid, series_instance_uid))
                return
            thumbnail_path = SeriesThumbnail.generate_thumbnail(study_instance_uid, series.series_instance_uid)
            series.thumbnail_path = thumbnail_path
            series.downloaded = True
            series.save()
        RabbitMQProducer.ws_notify("ws.notify.study_percent", notify_data)
        # 检查是否推送
        algorithm_task_qs = AlgorithmTask.objects.filter(series_uid__in=series_instance_uids)
        if algorithm_task_qs.exists():
            log.info("Study[{}] > toshiba ctp series has been pushed:{}".format(
                study_instance_uid, series_instance_uids))
            return
        # GE申请调度计算任务
        if self.ge_callback:
            calc_no = study_instance_uid
            ret_code = ge_apply_calc_schedule_notice(calc_no, "study", study_instance_uid)
            log.info(f"GE[applyCalcSchedule] > calc：{calc_no}, retCode:{ret_code}")
            return
        self.calculate_toshiba_series(study_instance_uid)

    @staticmethod
    def calculate_toshiba_series(study_instance_uid, resource_data=None):
        # 获取东芝CTP序列
        study = Study.objects.filter(study_instance_uid=study_instance_uid).first()
        series_queryset = study.series_set.filter(type=Const.ALGORITHM_TYPE_CTP).all()
        ctp_series_uid_list = [series.series_instance_uid for series in series_queryset]
        if AlgorithmTask.objects.filter(series_uid__in=ctp_series_uid_list).exists():
            log.info("Study[callback] > study:{}, toshiba ctp series has been pushed:{}".format(
                study_instance_uid, ctp_series_uid_list))
            if resource_data:
                storage_data(calcNo=study_instance_uid,
                             aiResultMsg={RetCode.STUDY_TOSHIBA_CTP_SERIES_HAS_BEEN_PUSHED.code,
                                          RetCode.STUDY_TOSHIBA_CTP_SERIES_HAS_BEEN_PUSHED.msg}, aiModel="")
            return RetCode.STUDY_TOSHIBA_CTP_SERIES_HAS_BEEN_PUSHED
        task_id_list = []
        # 创建算法任务
        for series_instance_uid in ctp_series_uid_list:
            algorithm_task = AlgorithmTask.objects.create(uuid=str(uuid.uuid1()), series_uid=series_instance_uid,
                                                          algorithm_type=Const.ALGORITHM_TYPE_CTP, user_id="admin")
            task_id_list.append(algorithm_task.uuid)
        # 获取算法队列名称
        queue_name = settings.TYPE_QUEUE_DICT.get(Const.ALGORITHM_TYPE_CTP, None)
        if not queue_name:
            log.info("Study[callback] > study:{}, algorithm queue not found, toshiba ctp series".format(
                study_instance_uid))
            if resource_data:
                storage_data(calcNo=study_instance_uid, aiModel=Const.ALGORITHM_TYPE_CTP,
                             aiResultMsg={RetCode.SERIES_ALGORITHM_QUEUE_NOT_FOUND.code,
                                          RetCode.SERIES_ALGORITHM_QUEUE_NOT_FOUND.msg})
            return RetCode.SERIES_ALGORITHM_QUEUE_NOT_FOUND
        # 组装消息
        message = dict(studyInstanceUID=study_instance_uid, seriesInstanceUIDs=ctp_series_uid_list,
                       algorithmType=Const.ALGORITHM_TYPE_CTP, toshiba=True)
        # GE资源信息
        if resource_data:
            message["resourceData"] = resource_data
        # 是否回传
        _back_config = SystemConfig.objects.filter(code="canReportBack").first()
        message["callback"] = eval(_back_config.value) if _back_config else True
        # 串并行
        message["taskType"] = SystemConfigUtils().getConfigValue(code="algorithmProcessMode", def_value="1")
        try:
            RabbitMQProducer.simple_send(queue_name=queue_name, message=message)
            log.info(
                "Study[callback] > study:{}, series:{}, algorithm: ctp(toshiba), queue_name: {}, message:{}".format(
                    study_instance_uid, ctp_series_uid_list, queue_name, message))
            return RetCode.OK
        except:
            log.error("Study[callback] > study:{}, series:{}, push error:{}".format(
                study_instance_uid, ctp_series_uid_list, traceback.format_exc()))
            # 发送失败清理算法任务
            AlgorithmTask.objects.filter(uuid__in=task_id_list).delete()
            if resource_data:
                storage_data(calcNo=study_instance_uid, aiModel=Const.ALGORITHM_TYPE_CTP,
                             aiResultMsg={RetCode.SERIES_PUSH_ERROR.code, RetCode.SERIES_PUSH_ERROR.msg})
            return RetCode.SERIES_PUSH_ERROR

class StudyCallbackService:

    def __init__(self, study_instance_uid, study_orthanc_id, hospital_uid, hospital_name):
        self.study_instance_uid = study_instance_uid
        self.study_orthanc_id = study_orthanc_id
        self.hospital_uid = hospital_uid
        self.hospital_name = hospital_name
        ge_callback = int(SystemConfigUtils.get_config(code=Config.GE_CALLBACK_SWITCH, def_value="0"))
        self.ge_callback = True if ge_callback else False

    def do_post(self):
        """
        检查回调请求处理入口
        :return:
        """
        study_qs = Study.objects.filter(hospital_uid=self.hospital_uid, study_instance_uid=self.study_instance_uid)
        if not study_qs.exists():
            log.error("Study[callback] > study[{}] not found".format(self.study_instance_uid))
            return RetCode.STUDY_NOT_FOUND
        study_qs.update(orthanc_id=self.study_orthanc_id)
        study = study_qs.first()
        # 当东芝开关关闭，忽略请求
        if not study.toshiba:
            log.info("Study[callback] > toshiba is false, ignore request")
            return RetCode.OK
        # 检测授权
        if not AuthVerify.check_module(Const.ALGORITHM_TYPE_CTP):
            Series.objects.filter(study__study_instance_uid=self.study_instance_uid).update(
                type=Const.ALGORITHM_TYPE_OTHER)
            if self.ge_callback:
                validate_dicom_failed(message=RetCode.STUDY_NOT_FOUND.msg, resource_type="study",
                                      resource_uid=self.study_instance_uid, ai_model=Const.ALGORITHM_TYPE_CTP)
            return RetCode.UNAUTHORIZED
        ctp_series_qs = Series.objects.filter(study__id=study.id, type=Const.ALGORITHM_TYPE_CTP)
        if ctp_series_qs.exists():
            threading.Thread(target=self.__async,
                             args=(study.id, self.ge_callback)).start()
        return RetCode.OK

    @staticmethod
    def __async(study_id, ge_callback):
        db.close_old_connections()
        handler = StudyPushHandler(study_id, ge_callback)
        handler.start()


class StudySeriesService:

    @staticmethod
    def get_series_v2(study_instance_uid):
        """
        获取序列信息

        :param study_instance_uid: 检查UID
        :return:
        """
        study = Study.objects.filter(study_instance_uid=study_instance_uid).first()
        if not study:
            log.info("Study[series] > study not found: {}".format(study_instance_uid))
            return RetCode.STUDY_NOT_FOUND, None
        data = dict(patientId=study.patient_id, patientName=study.patient_name, patientSex=study.patient_sex,
                    patientAge=study.patient_age, patientWeight=study.patient_weight,
                    patientBirthdate=study.patient_birthdate, studyInstanceUID=study_instance_uid,
                    studyId=study.id, is_confirmed=study.is_confirmed)
        series_list = study.series_set.order_by("original_series", "type", "series_description").all()
        log.info("Study[series] > study[{}], find {} series".format(study_instance_uid, len(series_list)))
        series_feature_map_dict = dict()
        feature_map_queryset = FeatureMap.objects.filter(study_instance_uid=study_instance_uid)
        for feature_map in feature_map_queryset:
            series_feature_map_dict[feature_map.series_instance_uid] = feature_map.to_dict()
        for series in series_list:
            group_key = StudySeriesService.__get_group_key(series.type)
            if group_key not in data:
                data[group_key] = []
            group_item = series.to_dict()
            if series.type in [Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA]:
                _status = Const.ALGORITHM_STATE_WAIT
                _task = AlgorithmTask.objects.filter(series_uid=series.series_instance_uid).first()
                if _task:
                    percent = _task.finish_percent
                    _status = Const.ALGORITHM_STATE_CALCULATING if percent < 100 else \
                        (Const.ALGORITHM_STATE_SUCCESS if percent == 100 else Const.ALGORITHM_STATE_FAILURE)
                group_item["status"] = _status
            if series.type == Const.ALGORITHM_TYPE_CTP and study.toshiba:
                group_item["toshiba"] = True
            # CTP 参数图
            if series.series_description in Const.CTP_FEATURE_MAPS:
                group_item.update(series_feature_map_dict.get(series.series_instance_uid, {}))
            # CTA
            if series.type == Const.ALGORITHM_TYPE_CTA:
                centerline_json_path = os.path.join(settings.DOCKER_DATA_BASE_DIR, "cta", study_instance_uid, series.series_instance_uid, "centerline.json")
                # 判断centerline.json的存在
                if os.path.exists(centerline_json_path):
                    group_item["cpr_path"] = centerline_json_path.replace("/code/data", "")
                cpr_path = StudySeriesService.__get_cpr(study_instance_uid, series.series_instance_uid, "/upixel")
                group_item["cpr"] = cpr_path
                mpr_path = f"/code/server/static/vti/{series.series_instance_uid}"
                group_item["mpr"] = F"/upixel/vti/{series.series_instance_uid}" if os.path.exists(mpr_path) else ""
                mpr_file_size = 0
                mpr_gz_dir = f"/code/server/static/vti/{series.series_instance_uid}/data"
                if os.path.exists(mpr_path):
                    path_list = os.listdir(mpr_gz_dir)

                    for file_name in path_list:
                        file_name_satrts = file_name.split(".gz")[0]
                        file_path = os.path.join(mpr_gz_dir, file_name_satrts)
                        gz_file_path = os.path.join(mpr_gz_dir, file_name)
                        if file_name.endswith(".gz") and os.path.exists(file_path) and os.path.exists(gz_file_path):
                            mpr_file_size = os.path.getsize(gz_file_path)
                            break
                group_item["mprFileSize"] = mpr_file_size

            if series.type == "cta_ar" and series.series_description.startswith("USC-UGuard CTA Volume"):
                group_item["jpgs"] = StudySeriesService.__get_jpg_list(study_instance_uid, series.original_series,
                                                                       series.series_description)
            data[group_key].append(group_item)
        return RetCode.OK, data

    @staticmethod
    def get_series(study_instance_uid):
        """
        获取序列信息

        :param study_instance_uid: 检查UID
        :return:
        """
        study = Study.objects.filter(study_instance_uid=study_instance_uid).first()
        if not study:
            log.info("Study[series] > study not found: {}".format(study_instance_uid))
            return RetCode.STUDY_NOT_FOUND, None
        data = dict(patientId=study.patient_id, patientName=study.patient_name, patientSex=study.patient_sex,
                    patientAge=study.patient_age, patientWeight=study.patient_weight,
                    patientBirthdate=study.patient_birthdate, studyInstanceUID=study_instance_uid, studyId=study.id)
        series_list = study.series_set.order_by("original_series", "type", "series_description").all()
        log.info("Study[series] > study[{}], find {} series".format(study_instance_uid, len(series_list)))
        series_feature_map_dict = dict()
        feature_map_queryset = FeatureMap.objects.filter(study_instance_uid=study_instance_uid)
        for feature_map in feature_map_queryset:
            series_feature_map_dict[feature_map.series_instance_uid] = feature_map.to_dict()
        for series in series_list:
            group_key = StudySeriesService.__get_group_key(series.type)
            if group_key not in data:
                data[group_key] = []
            group_item = series.to_dict()
            if series.type in [Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA]:
                _status = Const.ALGORITHM_STATE_WAIT
                _task = AlgorithmTask.objects.filter(series_uid=series.series_instance_uid).first()
                if _task:
                    percent = _task.finish_percent
                    _status = Const.ALGORITHM_STATE_CALCULATING if percent < 100 else \
                        (Const.ALGORITHM_STATE_SUCCESS if percent == 100 else Const.ALGORITHM_STATE_FAILURE)
                group_item["status"] = _status
            if series.type == Const.ALGORITHM_TYPE_CTP and study.toshiba:
                group_item["toshiba"] = True
            # CTP 参数图
            if series.series_description in Const.CTP_FEATURE_MAPS:
                group_item.update(series_feature_map_dict.get(series.series_instance_uid, {}))
            # CTA 组装VR图
            if series.series_description == "USC-UGuard CTA Volume Rendering":
                group_item["jpgs"] = StudySeriesService.__get_vr_list(study_instance_uid,
                                                                      series.series_instance_uid)
                group_item["cpr"] = StudySeriesService.__get_cpr(study_instance_uid, series.series_instance_uid,
                                                                 "/server/static")
            data[group_key].append(group_item)
        return RetCode.OK, data

    @staticmethod
    def __get_group_key(series_type):
        """
        获取序列分组

        :param series_type: 序列类型
        :return:
        """
        if not series_type:
            return "other"
        if "_" not in series_type:
            return series_type
        return series_type.split("_")[0]

    @staticmethod
    def __get_jpg_list(study_instance_uid, series_instance_uid, series_description):
        """
        获取VR列表

        :param study_instance_uid: 检查标识
        :param series_instance_uid: 序列标识
        :return:
        """
        desc_type_mapping = {
            "USC-UGuard CTA Volume Rendering AP": "VR_AP",
            "USC-UGuard CTA Volume Rendering LR": "VR_LR",
            "USC-UGuard CTA Volume MIP AP": "MIP_AP",
            "USC-UGuard CTA Volume MIP LR": "MIP_LR",
            "USC-UGuard CTA Volume Rendering Head AP": "VR_Head_AP",
            "USC-UGuard CTA Volume Rendering Head LR": "VR_Head_LR",
            "USC-UGuard CTA Volume MIP Head AP": "MIP_Head_AP",
            "USC-UGuard CTA Volume MIP Head LR": "MIP_Head_LR",
        }
        jpg_type = desc_type_mapping.get(series_description)
        if not jpg_type:
            return []
        vr_list = []
        vr_root_dir = "/upixel/jpg"
        new_dir = os.path.join(vr_root_dir, study_instance_uid, series_instance_uid)
        for angle in range(0, 360, 15):
            vr_list.append(F"{new_dir}/{jpg_type}_{angle}.jpg")
        return vr_list

    @staticmethod
    def __get_vr_list(study_instance_uid, series_instance_uid):
        """
        获取VR列表

        :param study_instance_uid: 检查标识
        :param series_instance_uid: 序列标识
        :return:
        """
        vr_list = [[], [], [], []]
        vr_root_dir = "/server/static/jpg"
        new_dir = os.path.join(vr_root_dir, study_instance_uid, series_instance_uid)
        if not os.path.exists(F"/code/{new_dir}"):
            StudySeriesService.__move_old_vr(study_instance_uid, series_instance_uid)
        for angle in range(0, 360, 15):
            vr_list[0].append(F"{new_dir}/VR_LR_{angle}.jpg")
            vr_list[1].append(F"{new_dir}/VR_AP_{angle}.jpg")
            vr_list[2].append(F"{new_dir}/MIP_LR_{angle}.jpg")
            vr_list[3].append(F"{new_dir}/MIP_AP_{angle}.jpg")
        return vr_list

    @staticmethod
    def __get_cpr(study_instance_uid, series_instance_uid, proxy_prefix):
        """
        获取VR列表

        :param study_instance_uid: 检查标识
        :param series_instance_uid: 序列标识
        :param proxy_prefix: 代理前缀
        :return:
        """
        cpr = {}
        cpr_dir = os.path.join("/code/server/static/cpr", study_instance_uid, series_instance_uid)
        if not os.path.exists(cpr_dir):
            return cpr
        file_list = os.listdir(cpr_dir)
        for filename in file_list:
            _filename = os.path.splitext(filename)[0]
            if _filename not in cpr:
                straighten_image_path = os.path.join(cpr_dir, "{}.vti".format(_filename))
                centerline_radius_path = os.path.join(cpr_dir, "{}.txt".format(_filename))
                stretched_center_line_path = os.path.join(cpr_dir, "{}.json".format(_filename))
                stretched_cpr_nii_path = os.path.join(settings.DOCKER_DATA_BASE_DIR, "cta", study_instance_uid, series_instance_uid, f"{_filename}_stretched_cpr.nii.gz")
                cpr[_filename] = {
                    "straightenImage": straighten_image_path.replace("/code/server/static", proxy_prefix) if os.path.exists(straighten_image_path) else "",
                    "centerlineRadius": centerline_radius_path.replace("/code/server/static", proxy_prefix) if os.path.exists(centerline_radius_path) else "",
                    "stretchedCenterline": stretched_center_line_path.replace("/code/server/static", proxy_prefix) if os.path.exists(stretched_center_line_path) else "",
                    "stretchedCprNii":  stretched_cpr_nii_path.replace(settings.DOCKER_DATA_BASE_DIR, "") if os.path.exists(stretched_cpr_nii_path) else ""
                }
        return cpr

    @staticmethod
    def __move_old_vr(study_instance_uid, series_instance_uid):
        """
        移动旧版本VR

        :param study_instance_uid: 检查UID
        :param series_instance_uid: 序列UID
        :return:
        """
        vr_root_dir = "/code/server/static/jpg"
        old_dir = os.path.join(vr_root_dir, series_instance_uid)
        new_dir = os.path.join(vr_root_dir, study_instance_uid, series_instance_uid)
        if not os.path.exists(new_dir):
            os.makedirs(new_dir)
        for root, dirs, files in os.walk(old_dir):
            for file in files:
                file_name_splitext = os.path.splitext(file)
                if file_name_splitext[1] != '.jpg':
                    log.warning("invalid file: {}".format(file))
                    continue
                jpg_path = os.path.join(root, file)
                new_file_name = "VR_" if root.__contains__("vr") else "MIP_"
                new_file_name += "AP_" if file.__contains__("AP") else "LR_"
                new_file_name += file_name_splitext[0].split("_")[-1] + file_name_splitext[1]
                log.info("{} : {}".format(file, new_file_name))
                new_file_path = os.path.join(new_dir, new_file_name)
                shutil.move(jpg_path, new_file_path)
        shutil.rmtree(old_dir)


class StudyRecalculateService:
    __SUPPORT_ALGORITHM = [Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA]

    @staticmethod
    def recalculate(study_instance_uid, request_body, bizData=None):
        """
        重新计算

        :param study_instance_uid: 检查UID
        :param request_body: 请求体
        :return:
        """
        log.info(f"Study[recalculate] > study_instance_uid{study_instance_uid}, request_body: {request_body}, bizData:{bizData}")
        study = Study.objects.filter(study_instance_uid=study_instance_uid).first()
        if not study:
            log.info("Study[callback] > study[{}] not found".format(study_instance_uid))
            return RetCode.STUDY_NOT_FOUND
        algorithm_type = request_body.get("algorithmType", "")
        # 全部算法重新计算
        if not algorithm_type:
            return StudyRecalculateService.__push_study(study)
        # 算法类型不为空，检验合法性
        if algorithm_type not in StudyRecalculateService.__SUPPORT_ALGORITHM:
            return RetCode.STUDY_INVALID_ALGORITHM_TYPE
        # Aspects和CTA重新计算
        if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS or algorithm_type == Const.ALGORITHM_TYPE_CTA:
            return StudyRecalculateService.__push_by_type(study, algorithm_type)
        # CTP重新计算
        aif_indexes = request_body.get("aifIndexes", [])
        vof_indexes = request_body.get("vofIndexes", [])
        series_instance_uid = request_body.get("seriesInstanceUID")
        if aif_indexes and vof_indexes:
            if not isinstance(aif_indexes, list) or len(aif_indexes) != 3:
                return RetCode.STUDY_INVALID_AIF_INDEXES
            if not isinstance(vof_indexes, list) or len(vof_indexes) != 3:
                return RetCode.STUDY_INVALID_VOF_INDEXES
        if study.toshiba or not series_instance_uid:
            return StudyRecalculateService.__push_by_type(study, algorithm_type, aif_indexes=aif_indexes,
                                                          vof_indexes=vof_indexes)
        # 序列选点计算
        series = Series.objects.filter(series_instance_uid=series_instance_uid, study__id=study.id).first()
        if not series:
            return RetCode.SERIES_NOT_FOUND
        if series.type != algorithm_type:
            return RetCode.SERIES_INVALID_ALGORITHM_TYPE
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if GE_CALLBACK_START == "1" and aif_indexes and vof_indexes:
            if study.toshiba:
                calcNo = study_instance_uid
                dicomResourceType = "study"
            else:
                calcNo = f"{study_instance_uid},{series_instance_uid}"
                dicomResourceType = "series"
            # 动静脉点选择计算触发的重新计算需要申请计算资源
            log.info("Study[recalculate] > study_instance_uid：{}， aif_indexes：{}，vof_indexes：{}  ".format(
                study_instance_uid, aif_indexes, vof_indexes))
            ge_apply_calc_schedule_notice(calcNo, dicomResourceType, series_instance_uid,
                                          bizData={"aif_indexes": aif_indexes, "vof_indexes": vof_indexes}, isRecalc=1)
        if bizData and isinstance(bizData, dict):
            aif_indexes = bizData.get("aif_indexes", [])
            vof_indexes = bizData.get("vof_indexes", [])
        return StudyRecalculateService.__push_by_type(study, algorithm_type, series=series, aif_indexes=aif_indexes,
                                                      vof_indexes=vof_indexes)

    @staticmethod
    def __push_study(study: Study):
        """
        重推检查

        :param study: 检查信息
        :return:
        """
        StudyRecalculateService.__rematch_other(study)
        algorithm_type_list = [Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA]
        study_algorithm_queryset = study.series_set.filter(type__in=algorithm_type_list).values_list("type", flat=True).distinct()
        if not study_algorithm_queryset:
            return RetCode.STUDY_ALGORITHM_TASK_NOT_FOUND
        algorithm_type_list = list(study_algorithm_queryset)
        log.info("Study[{}] > algorithms: {}".format(study.study_instance_uid, algorithm_type_list))
        for algorithm_type in algorithm_type_list:
            # Thread(target=StudyRecalculateService.__push_by_type, args=(study, algorithm_type)).start()
            ret_code = StudyRecalculateService.__push_by_type(study, algorithm_type)
            if ret_code.code != RetCode.OK.code:
                return ret_code
        return RetCode.OK

    @staticmethod
    def __push_by_type(study: Study, algorithm_type: str, series: Series = None, aif_indexes: list = None,
                       vof_indexes: list = None):
        """
        按算法类型推送

        :param study: 检查信息
        :param algorithm_type: 算法类型
        :param series: 指定序列
        :param aif_indexes: 动脉选点
        :param vof_indexes: 静脉选点
        :return:
        """
        log.info("Study[{}] > recalculate {}".format(
            study.study_instance_uid, algorithm_type))
        origin_uid_list, result_dict_list = StudyRecalculateService.__get_uid_list(study, algorithm_type, series)
        if len(origin_uid_list) == 0:
            return RetCode.STUDY_SERIES_NOT_FOUND
        task_queryset = AlgorithmTask.objects.filter(series_uid__in=origin_uid_list,
                                                     algorithm_type=algorithm_type)
        if not task_queryset:
            return RetCode.STUDY_ALGORITHM_TASK_NOT_FOUND
        for task in task_queryset:
            if task.finish_percent < 100:
                return RetCode.STUDY_ALGORITHM_TASK_PROCESSING
        log.info("Study[{}] > algorithm:{}, series:{}".format(
            study.study_instance_uid, algorithm_type, origin_uid_list))
        task_queryset.update(finish_percent=0, error_code=0)
        if algorithm_type == Const.ALGORITHM_TYPE_CTP:
            uid_list = [i.get("seriesInstanceUID") for i in result_dict_list]
            FeatureMap.objects.filter(study_instance_uid=study.study_instance_uid,
                                      series_instance_uid__in=uid_list).delete()
        # 清理
        StudyRecalculateService.__delete_result(study, origin_uid_list, algorithm_type)
        StudyRecalculateService.__delete_series(study.study_instance_uid, result_dict_list)
        # 检测授权
        if not AuthVerify.check_module(algorithm_type):
            task_queryset.delete()
            Series.objects.filter(study=study, type=algorithm_type).update(type=Const.ALGORITHM_TYPE_OTHER)
            # 模块授权检测失败，消息的目前为刷新列表
            notify_message = {"action": "new", "data": {"StudyInstanceUID": study.study_instance_uid}}
            RabbitMQProducer.ws_notify("ws.notify.study_create", notify_message)
            return RetCode.UNAUTHORIZED
        # 发Q
        send_result = StudyRecalculateService.__send_queue(study, origin_uid_list,
                                                           algorithm_type, aif_indexes, vof_indexes)
        if not send_result:
            return RetCode.STUDY_ALGORITHM_PUSH_ERROR
        Study.objects.filter(study_instance_uid=study.study_instance_uid).update(api_version="v2",
                                                                                 gmt_modified=datetime.datetime.now())
        notify_data = {"action": "update", "data": {
            "StudyInstanceUID": study.study_instance_uid, "downloadStatus": 1, "finishStatus": 0,
            "finishAlgorithms": "", "percent": {"type": algorithm_type, "value": 0}}}
        RabbitMQProducer.ws_notify("ws.notify.study_percent", notify_data)
        return RetCode.OK

    @staticmethod
    def __rematch_other(study):
        """
        Other序列重新匹配

        :param study:
        :return:
        """
        study_instance_uid = study.study_instance_uid
        other_series_queryset = Series.objects.filter(study__id=study.id, type=Const.ALGORITHM_TYPE_OTHER).order_by("gmt_modified").all()
        if not other_series_queryset.exists():
            log.info("recalculate[study:{}] > other series not found".format(study.study_instance_uid))
            return
        for series in other_series_queryset:
            series_instance_uid = series.series_instance_uid
            image = CallBackDICOM.objects.filter(study_instance_uid=study_instance_uid,
                                                 series_instance_uid=series_instance_uid,
                                                 sop_orthanc_uuid__isnull=False).first()
            tags = OrthancApi.find_tags(image.sop_orthanc_uuid)
            algorithm_type, comment = ImageCallbackService.match_algorithm(tags)
            log.info("recalculate[study:{}, series:{}] > type:{}, rematch algorithm:{}".format(
                study_instance_uid, series.series_instance_uid, Const.ALGORITHM_TYPE_OTHER, algorithm_type))
            # 记录序列算法匹配记录
            SeriesAlgorithm.objects.create(id=str(uuid.uuid1()), series_instance_uid=series_instance_uid,
                                           comment=json.dumps(comment))
            if not algorithm_type:
                continue
            # 检查CTP图像数量是否达标
            if algorithm_type == Const.ALGORITHM_TYPE_CTP:
                image_count_number = CallBackDICOM.objects.filter(study_instance_uid=study_instance_uid,
                                                                  series_instance_uid=series_instance_uid).count()
                min_ctp_image_num = SystemConfigUtils().getConfigValue(Config.CTP_IMAGE_NUMBER_MIN, 200)
                log.info("recalculate[study:{}, series:{}] > ctp number:{}, image number:{}".format(
                    study_instance_uid, series_instance_uid, min_ctp_image_num, image_count_number))
                if image_count_number < int(min_ctp_image_num):
                    log.info("recalculate[study:{}, series:{}] > insufficient number of images".format(
                        study_instance_uid, series_instance_uid))
                    continue
            # 检查ASPECTS层厚是否达标
            if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
                try:
                    slice_thickness = float(series.slice_thickness)
                    aspect_slice_thickness_min = int(SystemConfigUtils().getConfigValue(Config.ASPECT_SLICE_THICKNESS_MIN, 2))
                    aspect_slice_thickness_max = int(SystemConfigUtils().getConfigValue(Config.ASPECT_SLICE_THICKNESS_MAX, 6))
                    if not aspect_slice_thickness_min < slice_thickness <= aspect_slice_thickness_max:
                        log.info("recalculate[study:{}, series:{}] > image slice thickness out of range".format(
                            study_instance_uid, series_instance_uid))
                        continue
                except:
                    log.error("recalculate[study:{}, series:{}] > invalid slice thickness:{}".format(
                        study_instance_uid, series_instance_uid, series.slice_thickness))
            # 更新序列类型
            series.type = algorithm_type
            series.save()
            log.info("recalculate[study:{}, series:{}] > other series match algorithm:{}".format(
                study_instance_uid, series_instance_uid, algorithm_type))
            # 下载原图
            is_toshiba_ctp = (study.toshiba and algorithm_type == Const.ALGORITHM_TYPE_CTP)
            downloader = SeriesDownloader(study_instance_uid, series_instance_uid, series.orthanc_id, is_toshiba_ctp)
            if not downloader.run():
                log.info("recalculate[study:{}, series:{}] > failed to download series".format(
                    study_instance_uid, series_instance_uid))
                continue
            # 东芝更新缩略图
            if is_toshiba_ctp:
                series.thumbnail_path = SeriesThumbnail.generate_thumbnail(study_instance_uid, series_instance_uid)
                series.downloaded = True
                series.save()
                AlgorithmTask.objects.create(uuid=str(uuid.uuid1()), series_uid=series_instance_uid,
                                             algorithm_type=algorithm_type, finish_percent=500, user_id="admin")
                continue
            # 序列拆分检测
            series_split = [series]
            splitter = SeriesSplitter(series_instance_uid)
            new_series = splitter.split()
            log.info("recalculate[study:{}, series:{}] > split {} series".format(
                study_instance_uid, series_instance_uid, len(new_series)))
            series_split.extend(new_series)
            for _series in series_split:
                _series_instance_uid = _series.series_instance_uid
                _series.thumbnail_path = SeriesThumbnail.generate_thumbnail(study_instance_uid, _series_instance_uid)
                _series.downloaded = True
                _series.save()
                # 创建算法任务
                AlgorithmTask.objects.create(uuid=str(uuid.uuid1()), series_uid=_series_instance_uid,
                                             algorithm_type=_series.type, finish_percent=500, user_id="admin")


    @staticmethod
    def __get_uid_list(study: Study, algorithm_type: str, series: Series = None):
        """
        获取检查算法序列和结果序列标识信息

        :param study: 检查
        :param algorithm_type: 算法类型
        :param series: 序列
        :return:
        """
        origin_uid_list = []
        result_uid_list = []
        # 序列级查询
        if series:
            series_instance_uid = series.series_instance_uid
            origin_uid_list.append(series_instance_uid)
            series_queryset = Series.objects.filter(study__id=study.id).filter(
                Q(original_series=series_instance_uid) | Q(series_description="USC-UGuard CTP Total Summary"))
            for _series in series_queryset.all():
                result_uid_list.append({"seriesInstanceUID": _series.series_instance_uid,
                                        "seriesDescription": _series.series_description,
                                        "orthancID": _series.orthanc_id})
            return origin_uid_list, result_uid_list
        # 检查级查询
        for series in study.series_set.filter(type__startswith=algorithm_type).order_by("gmt_modified").all():
            series_type = series.type
            series_instance_uid = series.series_instance_uid
            if series_type == algorithm_type:
                origin_uid_list.append(series_instance_uid)
                continue
            result_uid_list.append({"seriesInstanceUID": series_instance_uid,
                                    "seriesDescription": series.series_description,
                                    "orthancID": series.orthanc_id})
        return origin_uid_list, result_uid_list

    @staticmethod
    def __delete_series(study_instance_uid, series_result_uid_list: list):
        """
        删除序列

        :param study_instance_uid: 检查UID
        :param series_result_uid_list: 算法结果列表，{@see __get_uid_list}
        :return:
        """
        if not series_result_uid_list or len(series_result_uid_list) == 0:
            log.info("series result not found, ignore cleanup")
            return
        for series in series_result_uid_list:
            series_instance_uid = series.get("seriesInstanceUID")
            series_orthanc_id = series.get("orthancID")
            if series_orthanc_id:
                # 删除图像结果
                OrthancApi.delete_series(series_orthanc_id)
            # 删除结果序列
            CallBackDICOM.objects.filter(study_instance_uid=study_instance_uid,
                                         series_instance_uid=series_instance_uid).delete()
            Series.objects.filter(series_instance_uid=series_instance_uid).delete()
            log.info("Study[recalculate] > Series[{}]({}), Orthanc[{}], deleted".format(
                series_instance_uid, series.get("seriesDescription"), series_orthanc_id))

    @staticmethod
    def __delete_result(study, series_uid_list, algorithm_type):
        """
        删除结果

        :param study: 检查
        :param series_uid_list: 序列标识
        :return:
        """
        study_instance_uid = study.study_instance_uid
        PDFReport.objects.filter(study_instance_uid=study_instance_uid).delete()
        mongodb = MongoDB()
        for series_instance_uid in series_uid_list:
            StudyRecalculateService.__delete_result_by_image_series(series_instance_uid, mongodb)
            result_dir = os.path.join(settings.DOCKER_DATA_BASE_DIR, algorithm_type, study_instance_uid,
                                      series_instance_uid)
            if os.path.exists(result_dir):
                shutil.rmtree(result_dir)
        if study.toshiba and algorithm_type == Const.ALGORITHM_TYPE_CTP:
            StudyRecalculateService.__delete_result_by_image_series(study_instance_uid, mongodb)

    @staticmethod
    def __delete_result_by_image_series(image_series, mongodb):
        """
        按图像序列删除算法结果

        :param image_series: 图像序列
        :param mongodb: MongoDB工具 {@see MongoDB}
        :return:
        """
        result_queryset = AlgorithmResult.objects.filter(image_series=image_series)
        if result_queryset:
            for result in result_queryset:
                result_id = result.algorithm_result
                if result_id:
                    mongodb.delete({"_id": ObjectId(result_id)}, "algorithm")
            result_queryset.delete()
            log.info("Study[recalculate] > ImageSeries[{}], Result, deleted".format(image_series))

    @staticmethod
    def __send_queue(study, series_uid_list, algorithm_type, aif_indexes=None, vof_indexes=None):
        """
        发送消息

        :param study: 检查
        :param series_uid_list: 序列UID列表
        :param algorithm_type: 算法类型
        :param aif_indexes: 动脉选点
        :param vof_indexes: 经脉选点
        :return:
        """
        study_instance_uid = study.study_instance_uid
        queue_name = settings.TYPE_QUEUE_DICT.get(algorithm_type, None)
        if not queue_name:
            log.error("queue not found: {}, algorithm type:{}".format(queue_name, algorithm_type))
            return False
        message = dict(studyInstanceUID=study_instance_uid, algorithmType=algorithm_type)
        if aif_indexes and vof_indexes:
            message["artery"] = aif_indexes
            message["vein"] = vof_indexes
        # 是否回传
        callback = SystemConfigUtils().getConfigValue(code="canReportBack", def_value="")
        message["callback"] = eval(callback) if callback else True
        # 串并行
        message["taskType"] = SystemConfigUtils().getConfigValue(code="algorithmProcessMode", def_value="1")
        log.info("study[callback] > study:{}, algorithm: {}, queue_name: {}, message: {}".format(
            study_instance_uid, algorithm_type, queue_name, message))
        try:
            if study.toshiba and algorithm_type == 'ctp':
                # 东芝灌注CTP数据
                message.update({"seriesInstanceUIDs": series_uid_list, "toshiba": True})
                RabbitMQProducer.simple_send(queue_name, message)
                return True
            for series_uid in series_uid_list:
                message["seriesInstanceUID"] = series_uid
                RabbitMQProducer.simple_send(queue_name, message)
            return True
        except Exception:
            log.error("failed to send message: {}".format(traceback.format_exc()))
            return False


class StudyReportService:

    @staticmethod
    def get_report(study_id, request_body):
        study = Study.objects.filter(id=study_id).first()
        if not study:
            log.debug("Study[report] > study not found, id:{}".format(study_id))
            return RetCode.STUDY_NOT_FOUND, None
        study_instance_uid = study.study_instance_uid
        series_ids = request_body.get("seriesIds", [])
        if not series_ids:
            log.debug("Study[report] > seriesIds not found".format(study_instance_uid))
            return RetCode.INCOMPLETE_PARAMETERS, None
        series_list = Series.objects.filter(
            study__id=study.id, id__in=series_ids,
            type__in=[Const.ALGORITHM_TYPE_ASPECTS, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA]).all()
        if len(series_list) != len(series_ids):
            log.debug("Study[report] > invalid series. seriesIds:{}, seriesSize:{}".format(
                study_instance_uid, series_ids, len(series_list)))
            return RetCode.INCOMPLETE_PARAMETERS, None
        # 如果保存过报告信息，取保存的信息
        queryset = PDFReport.objects.filter(study_instance_uid=study_instance_uid)
        if queryset.exists():
            log.info("Study[report] > query saved")
            return RetCode.OK, StudyReportService.__query_saved(study, queryset.first(), series_list)
        # 获取基本信息
        return RetCode.OK, StudyReportService.__query_first(study, series_list)

    @staticmethod
    def __query_saved(study, pdf_report, series_list):
        """
        查询保存的报告
        :param study:
        :param pdf_report:
        :param series_list:
        :return:
        """
        study_instance_uid = study.study_instance_uid
        response_body = dict(
            patientId=pdf_report.image_no,
            patientName=pdf_report.patient_name,
            patientSex=pdf_report.male,
            patientAge=pdf_report.age,
            studyId=pdf_report.check_no,
            studyDate=pdf_report.check_datetime,
            reportDate=pdf_report.report_date,
            reportDoctor=pdf_report.report_doctor,
            auditDoctor=pdf_report.audit_doctor
        )
        # 算法类型
        algorithms_dict = dict()
        for series in series_list:
            algorithm_type = series.type
            if algorithm_type not in algorithms_dict:
                algorithms_dict[algorithm_type] = []
            algorithms_dict[algorithm_type].append(series.series_instance_uid)
        ctp_report_merge = int(SystemConfigUtils().getConfigValue("ctpReportMerge", "2"))
        algorithm_size = len(algorithms_dict.keys())
        # 算法结果
        for algorithm_type in algorithms_dict:
            # 忽略重复算法
            if response_body.__contains__(algorithm_type):
                log.debug("Study[{}] > {} result have been obtained".format(
                    study_instance_uid, algorithm_type))
                continue
            series_uid_list = algorithms_dict[algorithm_type]
            jpgs = []
            if algorithm_type == Const.ALGORITHM_TYPE_CTA:
                if pdf_report.cta_image:
                    jpgs = json.loads(pdf_report.cta_image)
                images = StudyReportService.__get_optional_images(study, series_uid_list[0], algorithm_type)
                conclusion = pdf_report.cta_conclusion
                if algorithm_size == 1 and pdf_report.conclusion:
                    conclusion = pdf_report.conclusion
                response_body[algorithm_type] = dict(jpgs=jpgs, images=images, conclusion = conclusion)
                continue
            if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
                if pdf_report.ncct_image:
                    jpgs = json.loads(pdf_report.ncct_image)
                images = StudyReportService.__get_optional_images(study, series_uid_list[0], algorithm_type)
                conclusion = pdf_report.ncct_conclusion
                if algorithm_size >= 1 and pdf_report.conclusion:
                    conclusion = pdf_report.conclusion
                response_body[algorithm_type] = dict(result=pdf_report.ncct_report_text, jpgs=jpgs, images=images,
                                                     conclusion=conclusion)
                continue
            if algorithm_type == Const.ALGORITHM_TYPE_CTP:
                conclusion = pdf_report.ctp_conclusion
                if algorithm_size == 1 and pdf_report.conclusion:
                    conclusion = pdf_report.conclusion
                if pdf_report.ctp_image:
                    jpgs = json.loads(pdf_report.ctp_image)
                if study.toshiba or ctp_report_merge != 1:
                    images = StudyReportService.__get_optional_images(study, series_uid_list[0], algorithm_type)
                    response_body[algorithm_type] = dict(result=pdf_report.ctp_report_text, jpgs=jpgs,
                                                         images=images, conclusion = conclusion)
                    continue
                if ctp_report_merge == 1:
                    merge_series_uid_list = Series.objects.filter(
                        study__id=study.id, type=Const.ALGORITHM_TYPE_CTP).values_list(
                        "series_instance_uid", flat=True).order_by("gmt_modified")
                    first_series_uid = merge_series_uid_list[0]
                    result_1 = StudyReportService.__get_algorithm_result(study, first_series_uid)
                    result_2 = None
                    if len(series_uid_list) == 2:
                        second_series_uid = merge_series_uid_list[1]
                        result_2 = StudyReportService.__get_algorithm_result(study, second_series_uid)
                    if result_1 and result_2:
                        images = StudyReportService.__get_optional_images(study, first_series_uid, algorithm_type, True)
                        response_body[algorithm_type] = dict(result=pdf_report.ctp_report_text, jpgs=jpgs,
                                                             images=images, conclusion = conclusion)
                        continue
                    images = StudyReportService.__get_optional_images(study, first_series_uid, algorithm_type)
                    response_body[algorithm_type] = dict(result=pdf_report.ctp_report_text, jpgs=jpgs,
                                                         images=images, conclusion = conclusion)
        return response_body

    @staticmethod
    def __query_first(study, series_list):
        """
        查询报告信息

        :param study:
        :param series_list:
        :return:
        """
        study_instance_uid = study.study_instance_uid
        response_body = dict(
            patientId=study.patient_id,
            patientName=study.patient_name,
            patientSex=study.patient_sex,
            patientAge=study.patient_age,
            studyId=study.study_id,
            studyDate=study.study_datetime.strftime('%Y-%m-%d') if study.study_datetime else "",
            reportDate=datetime.datetime.now().strftime('%Y-%m-%d')
        )
        # 算法类型
        algorithm_dict = dict()
        for series in series_list:
            algorithm_type = series.type
            if algorithm_type not in algorithm_dict:
                algorithm_dict[algorithm_type] = []
            algorithm_dict[algorithm_type].append(series.series_instance_uid)
        ctp_report_merge = int(SystemConfigUtils().getConfigValue("ctpReportMerge", "2"))
        # 算法结果
        switch = {"aspects": StudyReportService.__handle_aspect, "cta": StudyReportService.__handle_cta,
                  "ctp": StudyReportService.__handle_ctp}
        for algorithm_type in algorithm_dict:
            # 忽略重复算法
            if response_body.__contains__(algorithm_type):
                log.debug("Study[{}] > {} result have been obtained".format(
                    study_instance_uid, algorithm_type))
                continue
            series_uid_list = algorithm_dict[algorithm_type]
             # CTA
            if algorithm_type == Const.ALGORITHM_TYPE_CTA:
                first_series_uid = series_uid_list[0]
                pending_result = {
                    "studyInstanceUID": study_instance_uid,
                    "seriesInstanceUID": first_series_uid,
                    "algorithmResult": {}
                }
                algorithm_seen = switch.get(algorithm_type)(pending_result)
                if algorithm_seen:
                    algorithm_seen["images"] = StudyReportService.__get_optional_images(
                        study, first_series_uid, algorithm_type)
                    response_body[algorithm_type] = algorithm_seen
                continue
            # ASPECTS
            if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
                first_series_uid = series_uid_list[0]
                algorithm_result = StudyReportService.__get_algorithm_result(study, first_series_uid)
                pending_result = {
                    "studyInstanceUID": study_instance_uid,
                    "seriesInstanceUID": first_series_uid,
                    "algorithmResult": algorithm_result
                }
                algorithm_seen = switch.get(algorithm_type)(pending_result)
                if algorithm_seen:
                    algorithm_seen["images"] = StudyReportService.__get_optional_images(
                        study, first_series_uid, algorithm_type)
                    response_body[algorithm_type] = algorithm_seen
                continue
            # CTP
            if algorithm_type == Const.ALGORITHM_TYPE_CTP:
                if study.toshiba or ctp_report_merge != 1:
                    first_series_uid = series_uid_list[0]
                    algorithm_result = StudyReportService.__get_algorithm_result(study, first_series_uid)
                    pending_result = {
                        "studyInstanceUID": study_instance_uid,
                        "seriesInstanceUIDs": [first_series_uid],
                        "isToshiba": study.toshiba,
                        "algorithmResult": algorithm_result
                    }
                    algorithm_seen = switch.get(algorithm_type)(pending_result)
                    if algorithm_seen:
                        algorithm_seen["images"] = StudyReportService.__get_optional_images(
                            study, first_series_uid, algorithm_type)
                        response_body[algorithm_type] = algorithm_seen
                    continue
                if ctp_report_merge == 1:
                    merge_series_uid_list = Series.objects.filter(
                        study__id=study.id, type=Const.ALGORITHM_TYPE_CTP).values_list(
                        "series_instance_uid", flat=True).order_by("gmt_modified")
                    first_series_uid = merge_series_uid_list[0]
                    result_1 = StudyReportService.__get_algorithm_result(study, first_series_uid)
                    result_2 = None
                    if len(merge_series_uid_list) == 2:
                        second_series_uid = merge_series_uid_list[1]
                        result_2 = StudyReportService.__get_algorithm_result(study, second_series_uid)
                    if result_1 and result_2:
                        total_tmax = []
                        total_cbf = []
                        for index in range(len(result_1["v_TMax"])):
                            total_tmax.append(result_1["v_TMax"][index] + result_2["v_TMax"][index])
                        for index in range(len(result_1["v_cbf"])):
                            total_cbf.append(result_1["v_cbf"][index] + result_2["v_cbf"][index])
                        algorithm_result = dict(v_TMax=total_tmax, v_cbf=total_cbf)
                        pending_result = {
                            "studyInstanceUID": study_instance_uid,
                            "seriesInstanceUIDs": merge_series_uid_list,
                            "algorithmResult": algorithm_result
                        }
                        total_summary = True
                    else:
                        pending_result = {
                            "studyInstanceUID": study_instance_uid,
                            "seriesInstanceUIDs": [first_series_uid],
                            "isToshiba": False,
                            "algorithmResult": result_1
                        }
                    algorithm_seen = switch.get(algorithm_type)(pending_result)
                    if algorithm_seen:
                        algorithm_seen["images"] = StudyReportService.__get_optional_images(
                            study, first_series_uid, algorithm_type, len(pending_result["seriesInstanceUIDs"]) == 2)
                        response_body[algorithm_type] = algorithm_seen
                    continue
        return response_body

    @staticmethod
    def __handle_aspect(result: dict):
        aspect_seen = {}
        # 获取结果图像
        study_instance_uid = result.get("studyInstanceUID")
        series_instance_uid = result.get("seriesInstanceUID", "")
        algorithm_result = result.get("algorithmResult")
        log.debug("Study[report] > study:{}, series:{}, aspects result: {}".format(
            study_instance_uid, series_instance_uid, algorithm_result))
        aspect_seen["jpgs"] = StudyReportService.__get_aspects_instance(study_instance_uid, series_instance_uid)
        # 获取算法结果
        area_desc = "ASPECT前循环分区:尾状核（C）豆状核（L）内囊（IC）岛叶皮质（I）大脑中动脉前皮质区（M1）大脑中动脉岛叶外侧皮质区" \
                    "（M2）大脑中动脉后皮层区（M3）M1上方的大脑中动脉皮层（M4）M2上方的大脑中动脉皮层（M5）M3上方的大脑中动脉皮层（M6）"
        # 单评分
        aspect_score = algorithm_result.get("infarct_result")
        if aspect_score:
            if aspect_score == 10:
                aspect_seen["result"] = "ASPECT评分：10分\n提示：影像结果提示正常。\n（）"
                return aspect_seen
            infarct_side = "左" if algorithm_result.get("infarct_side", "") == "left" else (
                "右" if algorithm_result.get("infarct_side", "") == "right" else "xx")
            infarct_areas = algorithm_result.get("infarct_areas", "xx")
            aspect_seen["result"] = F"ASPECT评分：{aspect_score}分(0-10分)" \
                                    F"\n提示：（{infarct_side}）侧可能存在缺血性改变。" \
                                    F"\n可能的病变区域为：{infarct_areas}\n（{area_desc}）"
            aspect_seen["conclusion"] = F"ASPECT评分为：{aspect_score}分，请结合临床"
            return aspect_seen
        # 双评分
        is_3d = "frontCycleScoreLeft" in algorithm_result and "frontCycleScoreRight" in algorithm_result
        score_left = algorithm_result.get("frontCycleScoreLeft" if is_3d else "scoreLeft", "")
        score_right = algorithm_result.get("frontCycleScoreRight" if is_3d else "scoreRight", "")
        area_text = ""
        if score_left < 10 or score_right < 10:
            if score_left < 10:
                left_area = algorithm_result.get("frontCycleInfarctAreasLeft" if is_3d else "infarctAreasLeft", "xx")
                area_text = F"左侧：{left_area}"
            if score_right < 10:
                right_area = algorithm_result.get("frontCycleInfarctAreasRight" if is_3d else "infarctAreasRight", "xx")
                area_text = "{}{}".format(
                    (area_text + "\n                              " if len(area_text) > 0 else ""),
                    F"右侧：{right_area}")
            area_text = "可能的病变区域为：" + area_text
        aspect_seen["result"] = F"ASPECT评分：左侧：{score_left}分 右侧：{score_right}分" \
                                F"\n{area_text}\n（{area_desc}）"
        aspect_seen["conclusion"] = F"ASPECT评分为：左侧{score_left}分，右侧{score_right}分，请结合临床"
        return aspect_seen

    @staticmethod
    def __get_aspects_instance(study_instance_uid, series_instance_uid):
        summary_path = F"/code/data/static/{study_instance_uid}/{series_instance_uid}/3D_ASPECTS_SUMMARY_USC/pic/1.jpg"
        if os.path.exists(summary_path):
            return [summary_path.replace("/code/data", "")]
        old_summary_path = F"/code/data/aspects/{study_instance_uid}/{series_instance_uid}/report.jpg"
        if os.path.exists(old_summary_path):
            return [old_summary_path.replace("/code/data", "")]
        return []

    @staticmethod
    def __handle_cta(result: dict):
        # 获取六方位VR图
        study_instance_uid = result.get("studyInstanceUID")
        series_instance_uid = result.get("seriesInstanceUID")
        log.debug("Study[report] > study:{}, series:{}, cta".format(
            study_instance_uid, series_instance_uid))
        jpgs = []
        if not os.path.exists(f"/code/server/static/jpg/{study_instance_uid}/{series_instance_uid}"):
            series_qs = Series.objects.filter(study__study_instance_uid=study_instance_uid, type="cta_ar")
            if not series_qs.exists():
                return {"jpgs": []}
            series_instance_uid = series_qs.first().series_instance_uid
            if not os.path.exists(f"/code/server/static/jpg/{study_instance_uid}/{series_instance_uid}"):
                return {"jpgs": []}
        root_path = os.path.join("/code/server/static/jpg", study_instance_uid, series_instance_uid)
        for i in range(4):
            angle = i * 90
            if (i % 2) == 1:
                _path = os.path.join(root_path, F"VR_Head_AP_{angle}.jpg")
                if not os.path.exists(_path):
                    _path = os.path.join(root_path, F"VR_AP_{angle}.jpg")
                jpgs.append(_path.replace("code/server/static", "upixel"))
        for i in range(4):
            angle = i * 90
            _path = os.path.join(root_path, F"VR_LR_{angle}.jpg")
            jpgs.append(_path.replace("code/server/static", "upixel"))
        return {"jpgs": jpgs, "conclusion":""}

    @staticmethod
    def __handle_ctp(result: dict):
        ctp_seen = {}
        # 获取结果图像
        study_instance_uid = result.get("studyInstanceUID")
        series_instance_uids = result.get("seriesInstanceUIDs", [])
        algorithm_result = result.get("algorithmResult")
        log.debug("Study[report] > study:{}, series:{}, ctp result: {}".format(
            study_instance_uid, series_instance_uids, algorithm_result))
        is_toshiba = result.get("isToshiba", False)
        if len(series_instance_uids) == 2:
            ctp_seen["jpgs"] = StudyReportService.__get_ctp_total_summary(study_instance_uid, series_instance_uids)
        else:
            ctp_seen["jpgs"] = StudyReportService.__get_ctp_summary(study_instance_uid, series_instance_uids[0], is_toshiba)
        # 低灌注体积Tmax>6s取Tmax第3值
        t_max = round(algorithm_result.get("v_TMax", '')[2], 2) if algorithm_result.get("v_TMax", '') else 0
        # 核心梗死体积CBF<30%取cbf第1值
        r_cbf = round(algorithm_result.get("v_cbf", '')[0], 2) if algorithm_result.get("v_cbf", '') else 0
        # 低灌和核心梗死不匹配体积
        t_max_cbf = round(t_max - r_cbf, 2)
        # 不匹配比
        ratio = round(t_max / r_cbf, 2) if t_max > 0 and r_cbf > 0 else "Inf"
        # 侧位：0是左脑，1是右脑
        affected_side = "左" if algorithm_result.get("affectedSide") == 0 else (
            "右" if algorithm_result.get("affectedSide") == 1 else "xx")
        # ctp_seen["affected_side"] = affected_side
        ctp_seen["result"] = F"低灌注体积（Tmax>6.0s的区域）为（{StudyReportService.delete_extra_zero(t_max)}）ml" \
                             F"\n核心梗死体积（rCBF<30%的区域）为（{StudyReportService.delete_extra_zero(r_cbf)}）ml" \
                             F"\n低灌和核心梗死不匹配体积（{StudyReportService.delete_extra_zero(t_max_cbf)}）ml" \
                             F"\n不匹配比（{StudyReportService.delete_extra_zero(ratio)}）"
        ctp_seen["conclusion"] = ""
        return ctp_seen

    @staticmethod
    def __get_ctp_summary(study_instance_uid, series_instance_uid, is_toshiba=False):
        parent_dir = F"/code/data/ctp/{study_instance_uid}"
        if not is_toshiba:
            parent_dir = F"{parent_dir}/{series_instance_uid}"
        summary_dir = F"{parent_dir}/png/USC_UGuard_CTP_Summary"
        if os.path.exists(summary_dir):
            prefix = summary_dir.replace("/code/data", "")
            jpgs = []
            for i in range(6):
                jpgs.append(F"{prefix}/img_{i}.png")
            return jpgs
        return []

    @staticmethod
    def __get_ctp_total_summary(study_instance_uid, series_instance_uids):
        parent_dir = F"/code/data/ctp/{study_instance_uid}"
        for series_instance_uid in series_instance_uids:
            total_summary_dir = F"{parent_dir}/{series_instance_uid}/png/USC_UGuard_CTP_Total_Summary"
            if os.path.exists(total_summary_dir):
                prefix = total_summary_dir.replace("/code/data", "")
                jpgs = []
                for i in range(6):
                    jpgs.append(F"{prefix}/img_{i}.png")
                return jpgs
        return []

    @staticmethod
    def delete_extra_zero(num):
        """
        删除小数点后多余的0

        :param num: 数字
        :return:
        """

        if isinstance(num, str):
            return num
        num = '{:g}'.format(num)
        # 含小数点转float否则int
        num = float(num) if '.' in num else int(num)
        return num

    @staticmethod
    def __get_algorithm_result(study, series_instance_uid):
        """
        获取算法结果

        :param study: 检查
        :param series_instance_uid: 序列标识
        :return:
        """
        study_uid = study.study_instance_uid
        algorithm_task = AlgorithmTask.objects.filter(series_uid=series_instance_uid).first()
        if not algorithm_task:
            log.debug("study[{}] > series:{}, task not found".format(study_uid, series_instance_uid))
            return None
        algorithm_type = algorithm_task.algorithm_type
        finish_percent = algorithm_task.finish_percent
        # 算法失败
        if finish_percent > 100:
            error_code = algorithm_task.error_code
            error_message = settings.ERROR_CODE.get(error_code, "")
            log.debug("study[{}] > series:{}, algorithm:{}, errorCode:{}({})".format(
                study_uid, series_instance_uid, algorithm_type, error_code, error_message))
            return None
        is_toshiba_ctp = algorithm_type == Const.ALGORITHM_TYPE_CTP and study.toshiba
        result_search = dict(image_series=study_uid) if is_toshiba_ctp else dict(task=algorithm_task)
        algorithm_result = AlgorithmResult.objects.filter(**result_search).first()
        if not algorithm_result:
            log.debug("study[{}] > series:{}, algorithm:{}, algorithm result not found.".format(
                study_uid, series_instance_uid, algorithm_type))
            return None
        # 获取算法结果
        algorithm_id = algorithm_result.algorithm_result
        mongodb = MongoDB()
        result_query = mongodb.get("algorithm", algorithm_id)
        if not result_query:
            log.debug("mongodb result not found")
            return None
        text_content = result_query.get("result", "{}")
        return json.loads(text_content)

    @staticmethod
    def __get_optional_images(study, series_instance_uid, algorithm_type, total_summary=False):
        """
        获取算法可选图片

        :param study: 检查
        :param series_instance_uid: 序列标识
        :param algorithm_type: 算法类型
        :param total_summary: CTP合并序列
        :return:
        """
        images = []
        study_instance_uid = study.study_instance_uid
        if algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
            root_path = os.path.join("/code/data/static", study_instance_uid, series_instance_uid)
            for sub_dir in ["3D_ASPECTS_SUMMARY_USC", "3D_PC-ASPECTS_SUMMARY_USC", "HEMORRHAGE_SUMMARY_USC"]:
                dir_path = os.path.join(root_path, sub_dir, "pic")
                if os.path.exists(dir_path):
                    for filename in sorted(os.listdir(dir_path)):
                        filepath = os.path.join(dir_path, filename)
                        images.append(filepath.replace("/code/data", ""))
            return images
        if algorithm_type == Const.ALGORITHM_TYPE_CTP:
            root_path = os.path.join("/code/data/ctp", study_instance_uid)
            if not study.toshiba and total_summary:
                for series_uid in os.listdir(root_path):
                    dir_path = os.path.join(root_path, series_uid, "png/USC_UGuard_CTP_Total_Summary")
                    if os.path.exists(dir_path):
                        for filename in sorted(os.listdir(dir_path)):
                            filepath = os.path.join(dir_path, filename)
                            images.append(filepath.replace("/code/data", ""))
                return images
            if not study.toshiba:
                root_path = os.path.join(root_path, series_instance_uid)
            root_path = os.path.join(root_path, "png")
            for sub_dir in ["USC_UGuard_CTP_Summary"]:
                dir_path = os.path.join(root_path, sub_dir)
                if os.path.exists(dir_path):
                    for filename in sorted(os.listdir(dir_path)):
                        filepath = os.path.join(dir_path, filename)
                        images.append(filepath.replace("/code/data", ""))
            return images
        root_path = os.path.join("/code/server/static/jpg", study_instance_uid, series_instance_uid)
        if os.path.exists(root_path):
            for filename in sorted(os.listdir(root_path)):
                filepath = os.path.join(root_path, filename)
                images.append(filepath.replace("code/server/static", "upixel"))
        return images


class DeleteViewTools:
    @staticmethod
    def create_delete_data(study_list, period):
        if not (study_list or period):
            log.info(f"Study[delete] > study_list: {study_list} or period: {period} not exists")
            return
        if study_list:
            log.info(f"Study[delete] > studyList: {study_list}")
            study_qs = Study.objects.filter(study_instance_uid__in=study_list)
            create_data = {"study_list": ",".join(study_list)}
        else:
            start_date, end_date = period.split("-")
            start_date = datetime.datetime.strptime(start_date, "%Y%m%d")
            end_date = datetime.datetime.strptime(end_date, "%Y%m%d")
            study_qs = Study.objects.filter(gmt_modified__date__range=(start_date, end_date))
            create_data = {"start_date": start_date, "end_date": end_date}

        if not study_qs.exists():
            log.info("Study[delete] > study not exists")
            return -1
        delete_study = DeleteStudy.objects.create(
            count=study_qs.count(),
            **create_data
        )
        log.info("Study[delete] > study_delete create success")
        return delete_study.id



