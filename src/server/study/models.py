#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : models
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/19 11:23
"""
import datetime

from django.db import models

# Create your models here.


class Hospital(models.Model):
    id = models.CharField(primary_key=True, max_length=255)  # 长度与旧表保持一致
    hospital_uid = models.CharField(max_length=64, unique=True, null=False, default="0", verbose_name="医院标识")
    hospital_name = models.CharField(max_length=64, null=False, default="0", verbose_name="医院名称")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    class Meta:
        db_table = "t_hospital"
        verbose_name = "医院信息表"
        verbose_name_plural = "hospitals"


class Study(models.Model):
    id = models.CharField(primary_key=True, max_length=255)  # 长度与旧表保持一致
    hospital_uid = models.CharField(max_length=64, null=False, default="0", verbose_name="医院标识")
    study_instance_uid = models.CharField(max_length=128, unique=True, null=False, verbose_name="检查标识")
    study_id = models.CharField(max_length=16, unique=True, null=False, default="", verbose_name="检查编号")
    study_datetime = models.DateTimeField(null=True, verbose_name="检查时间")
    study_description = models.CharField(max_length=255, null=False, default="", verbose_name="检查描述")
    patient_id = models.CharField(max_length=64, null=False, default="", verbose_name="患者编号")
    patient_name = models.CharField(max_length=64, null=False, default="", verbose_name="患者名称")
    patient_sex = models.CharField(max_length=10, null=False, default="", verbose_name="患者性别")
    patient_age = models.CharField(max_length=10, null=False, default="", verbose_name="患者年龄")
    patient_weight = models.CharField(max_length=16, null=False, default="", verbose_name="患者体重")
    patient_birthdate = models.CharField(max_length=8, null=False, default="", verbose_name="患者生日")
    api_version = models.CharField(max_length=8, null=False, default="", verbose_name="接口版本")
    toshiba = models.BooleanField(default=False, verbose_name="东芝标记")
    orthanc_id = models.CharField(max_length=255, null=False, default="", verbose_name="Orthanc标识")
    gmt_create = models.DateTimeField(default=datetime.datetime.now, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(default=datetime.datetime.now, verbose_name="更新时间")

    # 新增型检确认按钮
    is_confirmed = models.BooleanField(default=0, verbose_name="是否确认")

    class Meta:
        db_table = "t_study"
        verbose_name = "检查信息"
        verbose_name_plural = "studies"

    def to_dict(self):
        return dict(
            id=self.id, hospitalUID=self.hospital_uid,
            studyInstanceUID=self.study_instance_uid, studyDescription=self.study_description,
            patientId=self.patient_id, patientName=self.patient_name,
            interfaceType=self.api_version, toshiba=self.toshiba,
            receiveTime=self.gmt_modified.strftime('%Y-%m-%d %H:%M:%S'))


    def to_dict_for_list(self):
        return dict(
            id=self.id,
            StudyInstanceUID=self.study_instance_uid,
            PatientID=self.patient_id,
            PatientName=self.patient_name,
            StudyDescription=self.study_description,
            updatetimestamp=self.gmt_modified.strftime('%Y-%m-%d %H:%M:%S'),
        )


class DeleteStudy(models.Model):
    count = models.PositiveIntegerField(default=0, verbose_name="需要删除的总数")
    deleted_count = models.PositiveIntegerField(default=0, verbose_name="已删除的数量")
    start_date = models.DateTimeField(blank=True, null=True, verbose_name="开始时间")
    end_date = models.DateTimeField(blank=True, null=True, verbose_name="结束时间")
    study_list = models.TextField(default="", verbose_name="检查列表")
    is_finished = models.BooleanField(default=False, verbose_name="是否已完成")
    gmt_create = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = "t_delete_study"
        verbose_name = "检查删除表"
        verbose_name_plural = "检查删除表"


class DeleteStudyDetail(models.Model):
    delete_study = models.ForeignKey(DeleteStudy, on_delete=models.DO_NOTHING, verbose_name="主任务id")
    study_instance_uid = models.CharField(max_length=255, verbose_name="检查标识")
    mysql = models.NullBooleanField(blank=True, null=True, verbose_name="mysql")
    mongo = models.NullBooleanField(blank=True, null=True, verbose_name="mongo")
    static = models.NullBooleanField(blank=True, null=True, verbose_name="static")
    orthanc = models.NullBooleanField(blank=True, null=True, verbose_name="orthanc")
    gmt_create = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    gmt_modified = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    data_info = models.TextField(blank=True, null=True, verbose_name="存储数据")

    class Meta:
        db_table = "t_delete_study_detail"
        verbose_name = "检查删除详情表"
        verbose_name_plural = "检查删除详情表"
