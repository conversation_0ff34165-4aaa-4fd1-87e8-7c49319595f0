key = list('cooriwaunion326c')


# 加密/解密文本文件方法
def EncryptionTextFile(fileName):
    f = open(fileName, 'r')
    str = f.read()
    result = EncryptionStr(str)
    return result


# 加密/解密二进制文件方法
def EncryptionBinaryFile(fileName):
    f = open(fileName, 'rb')
    binary = f.read()
    result = EncryptionBinary(binary)
    return result


# 加密/解密字符串 Return:返回加密/解密后的字符串
def EncryptionStr(str):
    result = ''
    i = 0
    for c in str:
        c = chr(ord(c) ^ ord(key[i]))
        result += c
        if i >= len(key) - 1:
            i = 0
        else:
            i = i + 1
    return result


# 加密/解密bytes Return:返回加密/解密后的字符串
def EncryptionBinary(binary):
    result = ''
    i = 0
    for b in binary:
        b = b ^ ord(key[i])
        result += chr(b)
        if i >= len(key) - 1:
            i = 0
        else:
            i = i + 1
    return result.encode('iso-8859-1').decode('gbk')
