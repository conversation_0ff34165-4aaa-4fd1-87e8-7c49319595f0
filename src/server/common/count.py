from __future__ import absolute_import, unicode_literals

import json
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
import django

django.setup()
import traceback
import uuid
import importlib
from django.http.request import HttpRequest

async_models = importlib.import_module('server.async.models')


def send_count(message=None):
    def send_notification_wapper(fun):
        def wapper(*args, **kwargs):
            try:
                request = args[1]
                try:
                    data = json.loads(request.body)
                except Exception:
                    print(traceback.format_exc())
                    return fun(*args, **kwargs)

                tags = data.get("tags", {})
                sopinstanceuid = tags.get("SOPInstanceUID", "")
                seriesinstanceuid = tags.get("SeriesInstanceUID", "")
                if seriesinstanceuid and sopinstanceuid:
                    queryset = async_models.RecordTime.objects.filter(series_instance_uid=seriesinstanceuid,
                                                                      category="callback")
                    if queryset:
                        num = queryset.count()
                        if num < 2:
                            uuid_str = str(uuid.uuid1())
                            async_models.RecordTime.objects.create(uuid=uuid_str,
                                                                   sop_instance_uid=sopinstanceuid,
                                                                   series_instance_uid=seriesinstanceuid,
                                                                   category="callback")
                    else:
                        uuid_str = str(uuid.uuid1())
                        async_models.RecordTime.objects.create(uuid=uuid_str,
                                                               sop_instance_uid=sopinstanceuid,
                                                               series_instance_uid=seriesinstanceuid,
                                                               category="callback")
            except Exception:
                print(traceback.format_exc())
                return fun(*args, **kwargs)
            return fun(*args, **kwargs)

        return wapper

    return send_notification_wapper


@send_count()
def get_hh(*a):
    pass


if __name__ == '__main__':
    for i in range(1, 6):
        b = HttpRequest
        b.method = "POST"
        b.POST = {"name": "111", "tags": {"SOPInstanceUID": str(i), "SeriesInstanceUID": "12"}}
        get_hh({}, b)
    # print(HttpRequest)
