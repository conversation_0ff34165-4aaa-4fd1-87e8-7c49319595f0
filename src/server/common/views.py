# encoding: utf-8

import json
import re

from django.contrib.auth import authenticate, get_user_model
from django.core.handlers.wsgi import WSGIRequest
from django.http.response import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from server.common.base import TokenGenerator
from server.common.utils import MongoJSONEncoder

from .code import RetCode

USER_MODEL = get_user_model()
import traceback


class Permission(object):
    def has_permission(self, request, view):
        user = TokenBackend.authenticate(request)
        return user


def login_require(view):
    def _authenticate(*args, **kwargs):
        """
        登录认证装饰器
        """
        request = args[0]
        response = {
            'status': False,
            'code': 413,
            'message': 'Authenticate failed',
            'data': None,
            'token': None,
        }
        if not isinstance(request, WSGIRequest):
            request = args[1]
        try:
            user = authenticate(request, token=None)
            if user:
                return view(*args, **kwargs)
        except json.JSONDecodeError:
            response['message'] = 'request data decode error'
            response['code'] = 401
        response = JsonResponse(response)
        response['Access-Control-Allow-Origin'] = '*'
        return response

    return _authenticate


class TokenBackend(object):
    @classmethod
    def authenticate(cls, request, token=None):
        """
        验证用户登录
        """
        method = request.method
        username = ''
        token = request.META.get('HTTP_AUTHORIZATION', '')
        if token:
            token = str(token).split()[-1]
        if not token:
            return None
        valid_char = re.findall(r'\W', str(token))
        if len(valid_char) == 2:
            username = str(token).split('-', 2)[0]
        if username:
            user = USER_MODEL.objects.filter(username=username).first()
            if user:
                token_check = TokenGenerator()
                success = token_check.check_token(user, token)
                if success:
                    return user
        return None


class BaseView(View):
    @property
    def response(self):
        return {
            "status": False,
            "message": "",
            "code": 412
        }

    def ok(self, data=None, message="请求成功", status=True):
        response_body = self.response
        response_body['status'] = status
        response_body['message'] = message
        response_body['code'] = 200
        response_body['data'] = data
        return JsonResponse(response_body, encoder=MongoJSONEncoder)

    def fail(self, code=412, message="请求失败"):
        response_body = self.response
        response_body['status'] = False
        response_body['message'] = message
        response_body['code'] = code
        return JsonResponse(data=response_body,status=code)

    def of(self, code: RetCode, message=None, data=None):
        response_body = self.response
        response_body["code"] = code.code
        response_body["message"] = message if message else code.msg
        if code is RetCode.OK:
            response_body["status"] = True
        if data:
            response_body["data"] = data
        return JsonResponse(response_body)

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        content_type = 'application/json'
        if request.method in ['PUT', 'DELETE', 'POST']:
            if request.body:
                try:
                    body = request.body
                    if isinstance(request.body, bytes):
                        try:
                            body = str(request.body, 'utf8')
                        except Exception:
                            body = request.body
                    if isinstance(body, str):
                        try:
                            body = json.loads(body)
                        except Exception:
                            body = request.body
                    setattr(request, request.method, body)
                except Exception:
                    print(traceback.format_exc())
                    response = self.response
                    response['status'] = False
                    response['message'] = 'request data decode error'
                    response['code'] = 401
                    return JsonResponse(response)
            else:
                setattr(request, request.method, dict())
        response = super().dispatch(request, *args, **kwargs)
        response['Access-Control-Allow-Origin'] = '*'
        return response


class APIview(BaseView):
    @csrf_exempt
    # @login_require
    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        response['Access-Control-Allow-Origin'] = '*'
        return response
