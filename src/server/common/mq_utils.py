#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2021/1/20 下午2:35
# @File    : mq_utils.py

import pika
import json
import os
import traceback
import sys
import datetime
import pymongo

# from server.register.models import Register

project_path = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.append(project_path)
# sys.path.append("/code")
#
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
import django

django.setup()

from server.common.base import check_user_info
from server.common.mongoConnector import MongoDB
# from server.patient.models import Patient


_env = os.environ
MQ_HOST = _env.get("MQ_HOST", "***********")
MQ_PORT = _env.get("MQ_PORT", 5673)
MQ_USERNAME = _env.get("MQ_USERNAME", "admin")
MQ_PASSWORD = _env.get("MQ_PASSWORD", "admin")
MQ_CONSUMER_NAME = _env.get("MQ_DICOM_NAME", "logs")
MQ_SEND_NAME = _env.get("MQ_SEND_NAME", "logs")
MQ_PATIENT_QUEUE_NAME = 'patient_history'


class RabbitMQAPI:
    def __init__(self, queue_name=None, no_ack=False):
        self.queue_name = queue_name
        self.no_ack = no_ack

    @property
    def no_ack(self):
        return self._no_ack

    @no_ack.setter
    def no_ack(self, value):
        if not isinstance(value, bool):
            raise ValueError("no_ack must be boolean")
        self._no_ack = value

    def build_connection(self, is_consumer=False, queue_name=None):
        queue_name = queue_name or self.queue_name
        credentials = pika.PlainCredentials(MQ_USERNAME, MQ_PASSWORD)
        if is_consumer:
            connection_dict = dict(
                host=MQ_HOST,
                port=MQ_PORT,
                virtual_host='/',
                credentials=credentials,
                heartbeat_interval=0,
            )
        else:
            connection_dict = dict(
                host=MQ_HOST,
                port=MQ_PORT,
                credentials=credentials,
                blocked_connection_timeout=300,
            )
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(**connection_dict)
        )
        channel = connection.channel()
        try:
            channel.queue_declare(queue=queue_name, durable=True)
        except:
            channel = connection.channel()
            channel.queue_delete(queue=queue_name)
            channel.queue_declare(queue=queue_name, durable=True)
        return channel, connection

    def process_data_callback(self, channel, method, properties, body):
        """
        :param channel:
        :param method:
        :param properties:
        :param body:
        :return:
        """
        if not self.no_ack:
            channel.basic_ack(delivery_tag=method.delivery_tag)

        pass

    def consumer(self):
        channel, connection = self.build_connection(is_consumer=True)
        channel.basic_consume(
            self.process_data_callback, queue=self.queue_name, no_ack=self.no_ack
        )
        print('waiting for message To exit   press CTRL+C')
        channel.start_consuming()

    def producer(self, queue, body):
        channel, connection = self.build_connection(queue_name=queue)
        channel.basic_publish(
            exchange='',
            routing_key=queue,
            properties=pika.BasicProperties(
                content_type="application/json", delivery_mode=2
            ),
            body=json.dumps(body),
        )
        connection.close()
        return True


class MQConsumer(RabbitMQAPI):
    def __init__(self, name=MQ_CONSUMER_NAME, no_ack=False):
        super(MQConsumer, self).__init__(queue_name=name, no_ack=no_ack)
        super(MQConsumer, self).consumer()

    def process_data_callback(self, channel, method, properties, body):
        print(body)
        trace_id, data = self.data_body(body=body)
        if trace_id:
            print("trace_id, data", trace_id, "****", data)
            mongodb = MongoDB()
            result = mongodb.db["logs"].find_one(
                trace_id, sort=[("index", pymongo.DESCENDING)]
            )
            if result:
                max_index = result.get("index", 0)
                data["index"] = max_index + 1
            result_create_set = mongodb.create(data, 'logs')
            if not self.no_ack:
                channel.basic_ack(delivery_tag=method.delivery_tag)

    @staticmethod
    def data_body(body):
        """
                :param body:
                {
            "pacs_info": {
                "target_ip": "***********",
                "target_ae": "DockerOrthanc",
                "target_port": 4242
            },
            "query_type": "SERIES",
            "patient_info":{
                "StudyInstanceUID": "********.1107.5.1.4.53621.30000014070423572131200000418",
                "SeriesInstanceUID":"********.1107.5.1.4.53621.30000014070423244203100005720"
                }
        }

                :return:

        """
        try:
            data = json.loads(body)
        except Exception:
            print(traceback.format_exc())
            return False, "body解析错误", None
        if isinstance(data, (tuple, list)):
            return False, "body解析错误", None
        try:
            uuid_str = data.get("uuid", "")
            url = data.get("url", "")
            trace_id = uuid_str + url
        except Exception:
            print(traceback.format_exc())
            return False, "解析uuid，url转化trance_id失败"
        if not trace_id:
            return False, "解析uuid，url转化trance_id为空"
        data["trace_id"] = trace_id
        return dict(trace_id=trace_id), data


class MQClient(RabbitMQAPI):
    def __init__(self, *args, name=None, **kwargs):
        super(MQClient, self).__init__(queue_name=name)
        self.is_batch = None
        self.data = args if args else kwargs

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, value):
        try:
            if isinstance(value, dict):
                self._data = value
                self.is_batch = False
            elif isinstance(value, (list, tuple)):
                self.is_batch = True
                self._data = value
            else:
                self._data = json.loads(value)
                self.is_batch = False
        except Exception:
            print(traceback.format_exc())
            self._data = None

    def send_message(self):
        if self.data:
            print(self.is_batch)
            if not self.is_batch:
                status = self.producer(queue=self.queue_name, body=self.data)
                print(status)
            else:
                for i in self.data:
                    status = self.producer(queue=self.queue_name, body=i)
                    print(status)
        else:
            print("error")


def send_trace_log(request, **kwargs):
    """
     data = dict(uuid="222",
    #                 url="api/v1/result/",
    #                 body={
    #                     "username": "songyouli334",
    #                     "password": "123456"
    #                 },
    #                 method="POST",
    #                 timestamp="2018-1-1",
    #                 create_user=1,
    #                 status=True,
    #                 error_log=error_log,
    #                 function="创建算法",
    #                 response_data={
    #                     "username": "songyouli334",
    #                     "password": "123456"
    #                 },
    #                 type="api",
    #                 index=0,
    #                 trace_id="222api/v1/result/")
    """
    user = check_user_info(request)
    if kwargs:
        data = dict(
            uuid=kwargs.get("uuid", ''),
            body=kwargs["body"],
            response_data=kwargs["response"],
            type=kwargs.get("type", "api"),
            index=kwargs.get("index", 0),
            timestamp=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
            create_user=str(user.id) if user else "",
            method=request.method,
            url=request.path,
            trace_id=kwargs.get("uuid", '') + request.path,
            error_log=kwargs.get("error_log", ""),
            status=kwargs.get("status", False),
            function=kwargs.get("function", ""),
        )
        _client =MQClient(name=MQ_CONSUMER_NAME, **data)
        _client.send_message()


def judge_dict(original_dict: dict, new_dict: dict) -> tuple:
    """
    对比新字典和老字典的区别，并返回改变的信息和内容
    :param original_dict:原始的字典数据
    :param new_dict:新增字典数据
    :return:改变的信息，改变的内容
    """
    change_dict = {}
    change_message_list = []
    for new_key, new_value in new_dict.items():
        original_value = original_dict.get(new_key)
        if original_value != new_value:
            if isinstance(original_value, dict) and isinstance(new_value, dict):
                _change_message, _change_dict = judge_dict(original_value, new_value)
                change_message_list.extend(_change_message)
                change_dict[new_key] = _change_dict

            else:
                change_dict[new_key] = original_value
                change_message = f'{new_key}的值由{original_value}改为了{new_value}'
                change_message_list.append(change_message)
    message = ';'.join(change_message_list)
    return message, change_dict


def reduction(original_dict: dict, change_dict: dict) -> dict:
    """
    把老的内容根据改变的字典，进行还原
    :param original_dict: 原始的字典
    :param change_dict: 需要还原的内容
    :return: 还原后的dict
    """
    for key, value in change_dict.items():
        if isinstance(value, dict):
            reduction(original_dict[key], value)
        else:
            original_dict[key] = value
    return original_dict


# def send_patient_history(request, change_model, pk):
#
#     creator = check_user_info(request)
#     method = request.method
#     if change_model == 'Patient':
#         if method == 'PUT':
#             data = request.PUT
#             # 患者逻辑状态的更改
#             queryset = Patient.objects.filter(uuid=pk, is_delete=False)
#             name = data.get('name', '')
#             patient_id = data.get('patient_id', '')
#             personal_id = data.get('personal_id', '')
#             birthday = data.get('birthday', '')
#             address = data.get('address', '')
#             status = data.get('status', '')
#             treatment_status = data.get('treatment_status', '')
#             phone = data.get('phone', '')
#             doctor_id = data.get("doctor_id", '')
#             username = data.get("username", "")
#             creator_role = creator.role_type
#             creator_role_id = creator.id
#             # 患者用户表的更新对比
#             user_change_message, user_change_dict = '', {}
#             if creator_role in ["Doctor", "company"]:
#                 # 如果是医生或者员工修改患者，需要判断下是否需要修改患者的帐号
#                 if username:
#                     user_account = queryset.first().user
#                     if user_account:
#                         user_new_dict = {
#                             'username': username,
#                             'phone': phone,
#                             'full_name': name,
#                         }
#                         user_original_dict = user_account.to_dict()
#                         user_change_message, user_change_dict = judge_dict(user_original_dict, user_new_dict)
#             # 患者表的更新对比
#             patient_new_dict = dict(
#                 name=name,
#                 patient_id=patient_id,
#                 personal_id=personal_id,
#                 birthday=birthday if birthday else None,
#                 address=address,
#                 status=status,
#                 user_id=creator_role_id,
#                 treatment_status=treatment_status,
#                 phone=phone,
#                 doctor_id=doctor_id
#             )
#             patient_change_message, patient_change_dict = judge_dict(queryset.first().to_dict, patient_new_dict)
#             # 按次收费患者表更新对比
#             register_queryset = Register.objects.filter(upiexl_patient_uuid=pk)
#             register_original_dict = register_queryset.first().to_dict()
#             register_change_message, register_change_dict = judge_dict(register_original_dict, split_data)
#             # 把三个表改变的信息加起来
#             all_change_message = user_change_message + patient_change_message + register_change_message
#             # 把三个表改变的结果进行整合
#             all_change_dict = {'Patient': patient_change_dict,
#                                'User': user_change_dict,
#                                'Register': register_change_dict
#                                }
#         else:
#             change_dict = {'is_delete': True}
#             patient_queryset = Patient.objects.filter(uuid=pk)
#             _patient_account = patient_queryset.first().user
#             user_change_message, user_change_dict = judge_dict(_patient_account.to_dict(), change_dict)
#             patient_change_message, patient_change_dict = judge_dict(patient_queryset.first().to_dict, change_dict)
#             all_change_message = user_change_message + patient_change_message
#             all_change_dict = {'Patient': patient_change_dict,
#                                'User': user_change_dict,
#                                }
#     elif change_model == 'Record':
#         # 病例逻辑的改变
#         if method == 'PUT':
#             data = request.PUT
#             creator = check_user_info(request)
#             patient_id = data.get('patient_id', '')
#             process_id = data.get('process', '')
#             is_pdf = data.get('is_pdf', '')
#             is_stl = data.get('is_stl', '')
#             record_data = data.get('record_data', '')
#             # 更新发病时间
#             is_case_cycle = data.get('is_case_cycle', '')
#             group = int(data.get('group', 1))
#             comment = data.get('comment', '')


if __name__ == '__main__':
    # LogsConsumer()
    # try:
    #     1 / 0
    # except Exception:
    #     print(traceback.format_exc())
    #     error_log = str(traceback.format_exc())
    #     data = dict(uuid="222",
    #                 url="api/v1/result/",
    #                 body={
    #                     "username": "songyouli334",
    #                     "password": "123456"
    #                 },
    #                 method="POST",
    #                 timestamp="2018-1-1",
    #                 create_user="1",
    #                 status=True,
    #                 error_log=error_log,
    #                 function="创建算法",
    #                 response_data={
    #                     "username": "songyouli334",
    #                     "password": "123456"
    #                 },
    #                 type="api",
    #                 index=0,
    #                 trace_id="222api/v1/result/")
    #
    #     data["trace_id"] = "111" + data.get("url")
    #     # mongodb = MongoDB()
    #     # result_create_set = mongodb.create(data, 'logs')
    #     mongodb = MongoDB()
    #     a = dict(trace_id="test")
    #     result = mongodb.db["logs"].find_one(a, sort=[("index", pymongo.DESCENDING)])
    #     if result:
    #         b = result.get("index", None)
    #         print(b)
    #     print(result)
    # _consumer = LogsConsumer()
    # _consumer.consumer()
    pass
