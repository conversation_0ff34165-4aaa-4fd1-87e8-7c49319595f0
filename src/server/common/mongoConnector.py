# 封装MongoBD使用中的CURD操作
# C-Create
# return 结果可以用 result.inserted_id或者result.inserted_ids获取
# 保存的id列表和信息
# U-Update
# return 更新结果 传入需要更新的filter字段和更新的内容，可以用与多条记录更新
# 或者单挑记录更新
# R-Retrieve
# 查询操作，return结果，可通过pymongo对内容进行二次操作
# D-Delete
# 删除操作，通过批量删除和独立删除进行操作
import logging

from pymongo import MongoClient
from server import settings
from bson.objectid import ObjectId
from pymongo.results import UpdateResult

log = logging.getLogger("django")


class MongoDB(object):

    def __init__(self):
        self.client = MongoClient(settings.MONGODB_HOST, int(settings.MONGODB_PORT), username=settings.MONGODB_USER,
                                  password=settings.MONGODB_PASSWORD)
        self.db = self.client.erecord

    # Create方法
    def create(self, body, table):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(body, dict):
            result = self.db[table].insert_one(body)
            return result
        if isinstance(body, list):
            result = self.db[table].insert_many(body)
            return result

    # Update方法,还需要添加对于条件的控制
    def update(self, body, table,
               condition, condition_action=None,
               is_many=False):
        if not isinstance(table, str):
            table = str(table)
        set = '$set'
        if condition_action:
            set = '${}'.format(condition_action)
        content = {set: body}
        if is_many:
            result = self.db[table].update_many(condition, content)
            return result
        result = self.db[table].update_one(condition, content)
        return result

    # Retrieve方法，进行查询操作
    def query(self, condition, table, condition_action=None):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(condition, list):
            set = '{}'.format(condition_action)
            search = {set: {"$in": condition}}
            log.info("MongoDB[query] > {}".format(search))
            result = self.db[table].find(search)
            return result
        if condition_action:
            set = '{}'.format(condition_action)
            search = {set: condition}
            log.info("MongoDB[query] > {}".format(search))
            result = self.db[table].find(search)
            return result
    
    def query_one(self, condition: list, table, condition_action: str = ""):
        "condition_action这里是字段名"
        if not isinstance(table, str):
            table = str(table)
        if not condition and not condition_action:
            result = self.db[table].find_one()
        elif isinstance(condition, list):
            set = '{}'.format(condition_action)
            search = {set: {"$in": condition}}
            log.info("MongoDB[query_one] > {}".format(search))
            result = self.db[table].find_one(search)
        else:
            # 处理其他情况
            search = {condition_action: condition} if condition_action else condition
            log.info("MongoDB[query_one] > {}".format(search))
            result = self.db[table].find_one(search)
        return result
    
    def query_one_new(self, value, collection, field, sort=None, projection=None):
        """
        查询单条记录(支持排序和字段投影)

        Args:
            value: 查询值
            collection: 集合名
            field: 字段名
            sort: 排序配置,格式为 [(field_name, direction)]
                  direction: 1 升序, -1 降序
            projection: 投影配置,指定返回/排除的字段

        Returns:
            dict: 查询结果，如果没有找到则返回None
        """
        try:
            collection = self.db[collection]
            query = {field: value}
            
            if sort:
                # 使用 find().sort().limit(1) 代替 find_one() 以支持排序
                cursor = collection.find(query, projection)
                cursor = cursor.sort(sort)
                # 先转换为列表并检查是否有结果
                results = list(cursor.limit(1))
                return results[0] if results else None
            else:
                # 无需排序时直接使用 find_one
                return collection.find_one(query, projection)
                
        except Exception as e:
            log.error(f"MongoDB查询失败: {str(e)}", exc_info=True)
            return None

    # Delete方法
    def delete(self, condition, table, is_many=False, count=0):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(condition, dict):
            if not is_many:
                self.db[table].remove(condition)
            else:
                self.db[table].remove(condition, count)

    def bulk_delete(self, condition_list, table, condition_key):
        condition = {condition_key: {"$in": condition_list}}
        return self.db[table].remove(condition)

    def get(self, table, _id):
        return self.db[table].find_one({"_id": ObjectId(_id)})

    def update_one(self, table, condition, body, upsert=False):
        """
        更新单个文档，支持upsert操作
        
        Args:
            table (str): 集合名称
            condition (dict): 查询条件
            body (dict): 要更新的数据
            upsert (bool): 如果为True，在没有匹配文档时插入新文档
            
        Returns:
            UpdateResult: MongoDB更新操作的结果
        """
        if not isinstance(table, str):
            table = str(table)
        
        # 确保更新数据使用$set操作符
        if not any(key.startswith('$') for key in body):
            content = {'$set': body}
        else:
            content = body
            
        result = self.db[table].update_one(
            condition,
            content,
            upsert=upsert
        )
        return result


if __name__ == '__main__':
    # a = MongoDB()
    # mongodb = MongoDB()
    # results = mongodb.query(ObjectId("5f647eba54e028001133ad5f"), 'record', '_id')
    # print(results)
    # if results:
    #    print("HHHHH")
    # for i in results:
    #     print(i.get("content"))
    mongodb = MongoDB()
    # mongodb.db=
    # record_object_id = query.record_detail_id
    results = mongodb.query(ObjectId("5f652fa65a4fee000136cfe2"), 'algorithm', '_id')
    for i in results:
        print(i.get("result"))
