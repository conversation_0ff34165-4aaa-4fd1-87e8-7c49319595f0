#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import functools
import time
import logging
import traceback
from functools import wraps

log = logging.getLogger("django")


def request_retry(retry_times=3, sleep_time=2):
    """
    网络请求函数重复执行次数
    """

    def warpper(func):
        @wraps(func)
        def inner(*args, **kwargs):
            result = False
            for index in range(1, retry_times + 1):
                try:
                    result = func(*args, **kwargs)
                    if result in (200, 201):
                        log.info(f"{func.__name__} execute {index} time, result: {result}")
                        return result
                except:
                    time.sleep(sleep_time)
                    log.info(f"{func.__name__} execute {index} time, error: \n{traceback.format_exc()}")

            return result

        return inner

    return warpper
