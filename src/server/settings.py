"""
Django settings for server project.

Generated by 'django-admin startproject' using Django 2.0.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.0/ref/settings/
"""

import os
import pymysql


pymysql.install_as_MySQLdb()

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '^=anqo92^jf+xb3nq&yy&v!aibw-afjnh(gii25k1(e&wxyq%_'

# SECURITY WARNING: don't run with d2ebug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    # 'concurrent_log_handler',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'server.user',
    'server.study',
    'server.series',
    'server.image',
    'server.report',
    'server.algorithm',
    'server.async',
    'django_celery_beat',
    "server.mails",
    "server.systemconfig"
]

MIDDLEWARE = [
    'server.common.middleware.DisableCSRFCheck',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'server.urls'
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, "server/static/report/templates")],
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'server.wsgi.application'

# Password validation
# https://docs.djangoproject.com/en/2.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
# 添加配置文件变量
from server.config import *

# Internationalization
# https://docs.djangoproject.com/en/2.0/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

PER_PAGE_COUNT = 10

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.0/howto/static-files/


STATIC_URL = 'server/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'server/static')
STATIC_ROOT_URL = os.path.join(BASE_DIR, 'server/static')

AUTH_USER_MODEL = 'user.User'

PHOTO_UPLOAD_PATH = os.path.join(BASE_DIR, 'server/static', 'user', 'img')
UPLOAD_PATH = os.path.join(BASE_DIR, 'server/static', 'dcm', 'upload')
DOWNLOAD_PATH = os.path.join(BASE_DIR, 'server/static', 'dcm', 'temp')
LOCAL_DIC_PATH = os.path.join(BASE_DIR, 'server/static', 'dcm', 'local')
UPLOAD_PICTURE_PATH = os.path.join(BASE_DIR, 'server/static', 'upload_picture')
DOWNLOAD_XML_PATH = os.path.join(BASE_DIR, 'server/static', 'xml', 'download')
RECORD_UPLOAD_ROOT = os.path.join(BASE_DIR, 'server/static', 'record')

# STATICFILES_DIRS = (
#     os.path.join(BASE_DIR, 'server/static/'),
# )
LOG_DIR = os.path.join(os.environ.get("DOCKER_DATA_BASE_DIR", "/code/data"), 'log/ugs-api')
LOG_PATH = os.path.join(LOG_DIR, 'ugs-api.log')
if not os.path.exists(LOG_DIR):
    try:
        os.makedirs(LOG_DIR, exist_ok=True)
    except PermissionError:
        pass


# 过滤定时请求 /api/v1/async/pacspagelist/ 接口，日志输出
def skip_unreadable_posts(record):
    # print(">>>>>>>>>>>>", dir(record))
    if record.args and 'POST /api/v1/async/pacspagelist/ HTTP/1.1' in record.args:
        return False
    return True

_env = os.environ
LOCAL_WEB_SERVER_HOST = _env.get("LOCAL_WEB_SERVER_HOST", "webapi")
LOG_DEBUG = _env.get("LOG_DEBUG", "1")
LOG_FILE_SIZE = _env.get("LOG_FILE_SIZE")

try:
    if LOG_FILE_SIZE and isinstance(LOG_FILE_SIZE, str):
        LOG_FILE_SIZE = int(LOG_FILE_SIZE)
    else:
        LOG_FILE_SIZE = 30
except:
    LOG_FILE_SIZE = 30

LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,  # 是否禁用已经存在的日志器
    'filters': {
        'skip_unreadable_posts': {
            '()': 'django.utils.log.CallbackFilter',
            'callback': skip_unreadable_posts,
        }
    },

    'formatters': {
        'standard': {
            'format': f"%(asctime)s[{LOCAL_WEB_SERVER_HOST}][%(levelname)s][%(process)d-%(thread)d][%(filename)s-%(lineno)s]: %(message)s"
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': LOG_PATH,
            'filters': ['skip_unreadable_posts'],
            'formatter': 'standard',
            'maxBytes': 1024 * 1024 * LOG_FILE_SIZE,
            'backupCount': 3
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'filters': ['skip_unreadable_posts'],
            'formatter': 'standard'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'propagate': True,
            'level': 'DEBUG' if LOG_DEBUG == "1" else "INFO",
        }
    }
}

PASSWORD_RESET_TIMEOUT_DAYS = 1
AUTHENTICATION_BACKENDS = ['server.common.views.TokenBackend',
                           'django.contrib.auth.backends.ModelBackend']

# Database
# https://docs.djangoproject.com/en/2.0/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'cloud',
        'HOST': DB_HOST,
        'USER': DB_USER,
        'PASSWORD': DB_PASSWORD,
        'PORT': DB_PORT,
        'CHARSET': 'utf8mb4',
        'COLLATION': 'utf8mb4_general_ci'
    },
    'algorithm': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'algorithm',
        'HOST': DB_HOST,
        'USER': DB_USER,
        'PASSWORD': DB_PASSWORD,
        'PORT': DB_PORT,
        'CHARSET': 'utf8mb4',
        'COLLATION': 'utf8mb4_general_ci'
    }
}
DATABASE_ROUTERS = ['server.db_router.DataBaseRouter']
# Celery配置
CELERY_APP = 'server'
# 基本配置
CELERY_ACCEPT_CONTENT = ['application/json']

# Task设置
CELERY_TASK_SERIALIZER = 'json'

# Task执行设置
CELERY_TASK_IGNORE_RESULT = True

# Task结果backend设置
CELERY_RESULT_BACKEND = None
CELERY_RESULT_SERIALIZER = 'json'

# 消息路由设置
# CELERY_TASK_ROUTES = {'itsm.discovery.task.send': {'queue': 'send'}}
# 设置默认queue名称,默认celery
# CELERY_TASK_DEFAULT_QUEUE = 'celery'
# 配置celery启动的默认交换器名称,默认celery
# CELERY_TASK_DEFAULT_EXCHANGE = 'celery'
# 配置celery启动的交换器默认类型
# CELERY_TASK_DEFAULT_EXCHANGE_TYPE = 'direct'
# 配置路由key
# CELERY_TASK_DEFAULT_ROUTING_KEY = 'celery'
# 配置默认消息可持久化
# CELERY_TASK_DEFAULT_DELIVERY_MODE = 'persistent'

# Broker设置
# transport://userid:password@hostname:port/virtual_host
CELERY_BROKER_URL = 'amqp://{user}:{password}@{host}:{port}//'.format(
    user=MQ_USERNAME, password=MQ_PASSWORD, host=MQ_HOST, port=MQ_PORT)
# 设置建立连接超时时间,单位秒
CELERY_BROKER_CONNECTION_TIMEOUT = 15
# 也可以通过设置下面的方式,实现上面的效果
# CELERY_BROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 5.0}

# worker设置
# 指定导入任务模块

CELERY_IMPORTS = ("server.algorithm.task")
# 配置并发数量,默认等于CPU的核数
CELERY_WORKER_CONCURRENCY = 4
# 设置每个worker最大子进程数量
CELERY_WORKER_MAX_TASKS_PER_CHILD = 3
CELERY_BROKER_HEARTBEAT = 0
# logging设置
# 设置worker输出日志级别
CELERY_WORKER_REDIRECT_STDOUTS_LEVEL = 'INFO'

# beat设置
# 设置beat scheduler,默认celery.beat:PersistentScheduler,
# 可以通过celery beat -S 参数设置
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
# 设置任务检查间隔,比如任务发生变化了,多久检查一次变化,然后把还未执行的计划任务修改为最新的状态
# 单位为秒
# CELERY_BEAT_MAX_LOOP_INTERVAL = 5
CELERY_TIMEZONE = 'Asia/Shanghai'
CELERY_ENABLE_UTC = True
# DATA_UPLOAD_MAX_MEMORY_SIZE = 104857600
DATA_UPLOAD_MAX_MEMORY_SIZE = 524288000 # 500M

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = "smtp.qiye.aliyun.com"
# EMAIL_HOST_PASSWORD = "song1996714QAZ"
# EMAIL_HOST_USER = "<EMAIL>"
EMAIL_FROM = "强联智创<<EMAIL>>"
EMAIL_PORT = 465
EMAIL_USE_SSL = True
# ADMINS = [('ganlu', '<EMAIL>'), ('likaikai', '<EMAIL>')]
# EMAIL_PORT=""
client_crt_path = os.path.join(BASE_DIR, "server", "static", "tls", "client-cert.pem")
client_key_path = os.path.join(BASE_DIR, "server", "static", "tls", "client-key.pem")

API_VERSION = "1.8.2"
DUPLICATED_INSTANCES_FIX = int(_env.get("DUPLICATED_INSTANCES_FIX", "0"))
