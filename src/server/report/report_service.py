#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : report_service
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/7/19 15:15
"""
import importlib
import datetime
import logging
import random

import numpy as np
from pydicom import uid, Dataset
from PIL import Image
from pydicom._storage_sopclass_uids import CTImageStorage
PacsServer = importlib.import_module('server.async.models').PacsServer
from server.common.code import Config
from server.common.remote_api import ModalityService
from server.study.models import Study
from server.systemconfig.system_config_utils import SystemConfigUtils

log = logging.getLogger("django")


class ReportService:
    @staticmethod
    def __gen_child_uid(parent_instance_uid):
        """
        根据父UID生成子UID

        :param parent_instance_uid: 父UID
        :return: 子UID
        """
        tem_uid = parent_instance_uid.split('.')[:-1]
        tem_uid.append(str(random.randint(10 ** 37, 10 ** 38 - 1)))
        return '.'.join(tem_uid)

    @staticmethod
    def jpg2dcm(study_instance_uid, jpg_data):
        # 生成DICOM
        study_qs = Study.objects.filter(study_instance_uid=study_instance_uid)
        if not study_qs.exists():
            log.warning("Study[{}] > study not found".format(study_instance_uid))
            return None, "study not found"
        study = study_qs.first()
        modality = SystemConfigUtils.get_config(Config.ALGORITHM_RESULT_MODALITY, "OT")
        try:
            image = np.asarray(Image.open(jpg_data))
        except Exception:
            log.error("Study[{}] > failed to open image".format(study_instance_uid))
            return None, "failed to open image"
        ds = Dataset()
        ds.file_meta = Dataset()
        ds.file_meta.TransferSyntaxUID = uid.ExplicitVRLittleEndian
        ds.SOPClassUID = CTImageStorage
        ds.PatientID = study.patient_id
        ds.PatientName = study.patient_name
        if study.patient_sex:
            ds.PatientSex = study.patient_sex
        if study.patient_birthdate:
            ds.PatientBirthDate = study.patient_birthdate
        ds.StudyInstanceUID = study.study_instance_uid
        ds.Modality = modality
        ds.StationName = "USC-UGuard"
        ds.Manufacturer = "UnionStrong"
        ds.ManufacturerModelName = "UGuard"
        if study.study_datetime:
            ds.StudyDate = study.study_datetime.strftime('%Y-%m-%d')
            ds.StudyTime = study.study_datetime.strftime('%H:%M:%S')
        if study.study_description:
            ds.StudyDescription = study.study_description
        series_instance_uid = uid.generate_uid()
        ds.SeriesInstanceUID = series_instance_uid
        ds.SeriesDescription = "USC-UGuard PDF Report"
        now = datetime.datetime.now()
        create_date = now.strftime("%Y%m%d")
        create_time = now.strftime("%H%M%S")
        ds.SeriesDate = create_date
        ds.SeriesTime = create_time
        ds.SOPInstanceUID = ReportService.__gen_child_uid(series_instance_uid)
        ds.ContentDate = create_date
        ds.ContentTime = create_time
        ds.InstanceNumber = 1
        ds.is_little_endian = True
        ds.is_implicit_VR = False
        ds.PixelData = image.tostring()
        ds.Rows, ds.Columns, ds.SamplesPerPixel = image.shape
        ds.LossyImageCompression = "01"
        ds.LossyImageCompressionRatio = 10
        ds.LossyImageCompressionMethod = "ISO_10918_1"
        ds.PixelRepresentation = 0
        ds.BitsAllocated = 8
        ds.HighBit = 7
        ds.BitsStored = 8
        ds.PlanarConfiguration = 0
        ds.PhotometricInterpretation = "RGB"
        return ds, "success"

    def send(self, study_instance_uid, jpg_data):

        ds, message = self.jpg2dcm(study_instance_uid, jpg_data)
        if not ds:
            return False, message
        # 发送PACS
        pacs = PacsServer.objects.filter(alie_name="target_pacs").first()
        log.info("query pacs: {}".format(pacs))
        if not pacs:
            log.error("PACS[target_pacs] not found, please go to admin to configure.")
            return False, "PACS not found"
        result = ModalityService.send_image(ds, pacs.ip_address, pacs.port)
        return result, "" if result else "failed to send {}:{}".format(pacs.ip_address, pacs.port)

