import logging

from server.common.base import check_auth_code
from server.common.code import RetCode
from server.common.views import APIview
from .models import PDFReport
from .report_service import ReportService

log = logging.getLogger("django")


class ReportImageUpload(APIview):

    def post(self, request, study_instance_uid):
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        jpg_image = request.FILES.get("file")
        log.info("API[report/{}/upload] > receive jpg: {}".format(study_instance_uid, jpg_image.name))
        jpg_name = jpg_image.name.lower()
        if not jpg_name.endswith(".jpg") and not jpg_name.endswith(".jpeg"):
            return self.fail(message="only jpg images are supported")
        report_service = ReportService()
        report_service.send(study_instance_uid, jpg_image)
        return self.ok()


class SaveReport(APIview):
    def post(self, request):
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        json = request.POST
        study_id = json["studyId"]
        # 为了避免一个study有多个report，每次保存前都先将study所有的report清空
        deleted, row_count = PDFReport.objects.filter(study_id=study_id).delete()
        log.info("report > study:{}, delete:{}, rowCount:{}".format(study_id, deleted, row_count))
        pdf_report = PDFReport.create_from_json(json)
        pdf_report.save()
        return self.ok()
