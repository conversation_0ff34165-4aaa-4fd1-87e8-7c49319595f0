# !/usr/bin/env python
# -*- coding: UTF-8 -*-
import hashlib
import importlib
import traceback
from ast import literal_eval

import pydicom
import requests
from requests.auth import HTTPBasicAuth
from django.conf import settings
from pynetdicom import AE, StoragePresentationContexts
from pydicom.uid import (ExplicitVRLittleEndian, ImplicitVRLittleEndian, ExplicitVRBigEndian,
                         DeflatedExplicitVRLittleEndian, JPEGBaseline, JPEGExtended, JPEGLosslessP14,
                         JPEG<PERSON><PERSON><PERSON>less, JPEG<PERSON><PERSON>ossy, JPEG2000<PERSON>ossless,
                         JPEG2000, JPEG2000MultiComponentLossless, JPEG2000MultiComponent, RLELossless)
import os

from server.series.models import Series
from server.systemconfig.models import SystemConfig
from server.common.code import Const

_transfer_syntax_uid = [
    ImplicitVRLittleEndian,
    ExplicitVRLittleEndian,
    ExplicitVRBigEndian,
    DeflatedExplicit<PERSON><PERSON><PERSON><PERSON>ndi<PERSON>,
    JP<PERSON>B<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>P14,
    # <PERSON><PERSON><PERSON><PERSON><PERSON>SV1,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>EG2000,
    JPEG2000Multi<PERSON>ompo<PERSON>Lossless,
    JPEG2000MultiComponent,
    RLELossless,
]

PacsServer = importlib.import_module('server.async.models').PacsServer
import logging

log = logging.getLogger("django")


class BackReport:
    _BASE_URL = F"http://{settings.ORTHANC_HOST}:{settings.ORTHANC_WEB_PORT}"
    _AUTH = HTTPBasicAuth(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD)
    _TIMEOUT = 30
    LOCAL_AET = settings.LOCAL_AET

    def __init__(self, study_instance_uid, series_instance_uid, algorithm_type, toshiba):
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.algorithm_type = algorithm_type
        self.toshiba = toshiba

    @staticmethod
    def get_target_aet():
        pacs_server = PacsServer.objects.filter(alie_name="target_pacs")
        target_aet, target_host, target_port = "", "", ""
        if pacs_server.exists():
            _server = pacs_server.first()
            target_aet = _server.aet
            target_host = _server.ip_address
            target_port = _server.port
        return target_aet, target_host, target_port

    @staticmethod
    def get_algorithm_report_value():
        code_map = {
            # cta
            "send_vr": "reportCtaVr",
            "send_mip": "reportCtaMip",
            "send_cs": "reportCtaCsMip",

            # ctp
            "can_send_summary": "reportCtpSummary",
            "can_send_colormap": "reportCtpColorMap",
            "can_send_mip": "reportCtpMip",

            # aspects
            "ncct_can_send_summary": "reportAspectsSummary",

        }
        value_map = {}
        for key, code in code_map.items():

            system_config = SystemConfig.objects.filter(code=code)
            value = False
            if system_config.exists():
                value = system_config.first().value
                try:
                    value = literal_eval(value)
                except:
                    log.info(f"BackReport > get system config code {code} error :{traceback.format_exc()}")

            value_map.setdefault(key, value)
        return value_map

    @staticmethod
    def get_orthanc_id(text: str):
        sha = hashlib.sha1(text.encode("utf-8"))
        encrypts = sha.hexdigest()
        chunks = [encrypts[i:i + 8] for i in range(0, len(encrypts), 8)]
        return "-".join(chunks)

    @staticmethod
    def move(data):
        try:
            url = F"{BackReport._BASE_URL}/modalities/local/move"
            response = requests.post(url=url, json=data, auth=BackReport._AUTH)
            log.info("Orthanc[response] > code:{}".format(response.status_code))
            success = response.status_code == 200
            if not success:
                log.info("Orthanc[request] > url:{}, data:{}, code:{}".format(url, data, response.status_code))
            return success
        except:
            log.error("cmove error: {}".format(traceback.format_exc()))
            return False

    @staticmethod
    def upload_image(file_path):
        # url = F"http://{settings.ORTHANC_HOST}:8042/instances/"
        url = F"{BackReport._BASE_URL}/instances/"
        try:
            with open(file_path, 'rb') as file:
                files = {'files': (FileUtils.get_filename_without_suffix(file_path), file,
                                   'application/octet-stream', {'Expires': '0'})}
                response = requests.post(url, files=files, auth=BackReport._AUTH)
                success = response.status_code == 200
                if not success:
                    log.info("BackReport > upload image, url:{}, file: {}, code:{}".format(
                        url, file_path, response.status_code))
                return success
        except Exception:
            log.error("BackReport > failed to call orthanc restful, file:{}, {}".format(file_path, traceback.format_exc()))
            return False

    @staticmethod
    def send_cstore(files, host, port, aet):
        log.info(f"BackReport > start c_store, files: {files}")
        ae = AE(ae_title=BackReport.LOCAL_AET)
        ae.dimse_timeout = 60
        for _context in StoragePresentationContexts:
            ae.add_requested_context(_context.abstract_syntax, _transfer_syntax_uid)
        assoc = ae.associate(host, port, ae_title=aet)
        log.info(f"BackReport > assoc.is_established: {assoc.is_established}")
        if assoc.is_established:
            send_status = True
            for file in files:
                dataset = pydicom.dcmread(file)
                try:
                    send_status = assoc.send_c_store(dataset)
                    log.info("BackReport > {} first send {} ".format(file, (send_status and send_status.get("Status") == 0)))
                except:
                    log.error("failed to send dicom: {}".format(traceback.format_exc()))
                    log.debug("BackReport > reassociate again")
                    assoc.release()
                    ae.add_requested_context(dataset.SOPClassUID, dataset.file_meta.TransferSyntaxUID)
                    assoc = ae.associate(host, port, ae_title=aet)
                    if assoc.is_established:
                        send_status = assoc.send_c_store(dataset)
                        log.info("BackReport > {} second send {}".format(file, (send_status and send_status.get("Status") == 0)))
                    else:
                        return False
            assoc.release()
            return send_status
        else:
            return False

    def cta_back_report(self, method, target_host, target_port, target_aet, value_map, series_desc_list):
        send_vr = value_map.get("send_vr")
        send_mip = value_map.get("send_mip")
        send_cs = value_map.get("send_cs")

        # cta
        # 手动获取所有的StudyUid, SeriesaUid  及文件path， 然后进行自动/手动  c_move/c_store

        cta_map = {
            "collateral circulation USC": "USC_UGuard_CTA_Volume_CS_MIP/",
            "USC-UGuard CTA Volume Rendering AP": "dcm/VR_AP/",
            "USC-UGuard CTA Volume Rendering LR": "dcm/VR_LR/",
            "USC-UGuard CTA Volume MIP AP": "dcm/MIP_AP/",
            "USC-UGuard CTA Volume MIP LR": "dcm/MIP_LR/",
            "USC-UGuard CTA Volume Rendering Head AP": "dcm/VR_Head_AP/",
            "USC-UGuard CTA Volume Rendering Head LR": "dcm/VR_Head_LR/",
            "USC-UGuard CTA Volume MIP Head AP": "dcm/MIP_Head_AP/",
            "USC-UGuard CTA Volume MIP Head LR": "dcm/MIP_Head_LR/",
        }
        if not series_desc_list:
            cta_dict = cta_map
        else:
            cta_dict = {}
            for series_desc, path_name in cta_map.items():
                if series_desc in series_desc_list:
                    cta_dict.setdefault(series_desc, path_name)
        log.debug(f"BackReport > cta_dict: {cta_dict}")
        resources = []
        all_files = []
        for series_description, path in cta_dict.items():
            series_qs = Series.objects.filter(
                study__study_instance_uid=self.study_instance_uid,
                original_series=self.series_instance_uid,
                series_description=series_description)
            if not series_qs.exists():
                log.debug(f"BackReport > series not exists: series_description: {series_description}, path:{path}")
                continue
            series = series_qs.first()
            series_uid = series.series_instance_uid
            base_dir = "/data/ctpdata" if os.path.exists("/data/ctpdata") else "/code/data"
            dcm_path = f"{base_dir}/cta/{self.study_instance_uid}/{self.series_instance_uid}/{path}"
            if series_desc_list or (send_vr and "VR_" in dcm_path) or (send_mip and ("/MIP_" in dcm_path or "Head_MIP" in dcm_path)) \
                    or (send_cs and "CS_MIP" in dcm_path):
                if series_description == "collateral circulation USC" and not os.path.exists(dcm_path):
                    dcm_path = os.path.join(base_dir, "static", self.study_instance_uid, self.series_instance_uid,
                                            "COLLATERAL_CIRCULATION_USC/dcm")
                # c_move
                log.debug(f"BackReport > dcm_path: {dcm_path}")
                files = FileUtils.get_all_file(dcm_path)
                all_files.extend(files)
                if method == "orthanc":
                    for file in files:
                        upload_result = BackReport.upload_image(file)
                        if not upload_result:
                            log.info("upload_image error")
                            return upload_result
                    resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
                    concatenation = "{}|{}|{}".format(series.study.patient_id, self.study_instance_uid, series_uid)
                    series.orthanc_id = self.get_orthanc_id(concatenation)
                    series.save()
        if method == "orthanc":
            data = {"Level": "SERIES", "TargetAet": target_aet, "Timeout": 60, "Resources": resources}
            return BackReport.move(data)
        return BackReport.send_cstore(all_files, target_host, target_port, target_aet)

    def ctp_back_report(self, method, target_host, target_port, target_aet, value_map, series_desc_list=None):
        ctp_map = {
            "USC-UGuard AIF-VOF Location": "USC_UGuard_CTP_AIF_VOF_CSF_Location",
            "USC-UGuard CTP Summary": "USC_UGuard_CTP_Summary",
            "USC-UGuard CTP Total Summary": "USC_UGuard_CTP_Total_Summary",
            "USC-UGuard Perfusion Parameter Maps Colored CBF": "USC_UGuard_CTP_Parameter_Colored_Map_CBF",
            "USC-UGuard Perfusion Parameter Maps Colored CBV": "USC_UGuard_CTP_Parameter_Colored_Map_CBV",
            "USC-UGuard Perfusion Parameter Maps Colored MTT": "USC_UGuard_CTP_Parameter_Colored_Map_MTT",
            "USC-UGuard Perfusion Parameter Maps Colored PS": "USC_UGuard_CTP_Parameter_Colored_Map_PS",
            "USC-UGuard Perfusion Parameter Maps Colored TTP": "USC_UGuard_CTP_Parameter_Colored_Map_TTP",
            "USC-UGuard Perfusion Parameter Maps Colored Tmax": "USC_UGuard_CTP_Parameter_Colored_Map_Tmax",
            "USC-UGuard CTP MIP": "USC_UGuard_CTP_MIP"
        }
        if not series_desc_list:
            ctp_dict = ctp_map
        else:
            ctp_dict = {}
            for series_desc, path_name in ctp_map.items():
                if series_desc in series_desc_list:
                    ctp_dict.setdefault(series_desc, path_name)

        log.debug(f"BackReport > ctp_dict: {ctp_dict}")
        can_send_summary = value_map.get("can_send_summary")
        can_send_colormap = value_map.get("can_send_colormap")
        can_send_mip = value_map.get("can_send_mip")

        resources = []
        all_files = []
        for series_description, dir_name in ctp_dict.items():
            search_data = {"study__study_instance_uid": self.study_instance_uid,
                           "series_description": series_description}
            if not self.toshiba and series_description != Const.ALGORITHM_SERIES_CTP_TOTAL_SUMMARY:
                search_data["original_series"] = self.series_instance_uid
            series_qs = Series.objects.filter(**search_data)
            if not series_qs.exists():
                log.debug(f"BackReport > series not exists: series_description: {series_description}, path:{dir_name}")
                continue
            series = series_qs.first()
            series_uid = series.series_instance_uid
            base_dir = "/data/ctpdata" if os.path.exists("/data/ctpdata") else "/code/data"
            dcm_path = f"{base_dir}/ctp/{self.study_instance_uid}/report/{dir_name}/" if self.toshiba else f"{base_dir}/ctp/{self.study_instance_uid}/{self.series_instance_uid}/report/{dir_name}/"
            if not os.path.exists(dcm_path):
                log.debug("{} not found".format(dcm_path))
                continue
            if series_desc_list or ("Summary" in dir_name and can_send_summary) or (
                    "Colored_Map" in dir_name and can_send_colormap) or (
                    ("MIP" in dir_name or "AIF_VOF" in dir_name) and can_send_mip):
                # c_move
                log.debug(f"BackReport > dcm_path: {dcm_path}")
                files = FileUtils.get_all_file(dcm_path)
                all_files.extend(files)
                if method == "orthanc":
                    for file in files:
                        upload_result = BackReport.upload_image(file)
                        if not upload_result:
                            log.debug("upload_image error")
                            return upload_result
                    resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
                    concatenation = "{}|{}|{}".format(series.study.patient_id, self.study_instance_uid, series_uid)
                    series.orthanc_id = self.get_orthanc_id(concatenation)
                    series.save()
        if method == "orthanc":
            data = {"Level": "SERIES", "TargetAet": target_aet, "Timeout": 60, "Resources": resources}
            return BackReport.move(data)
        return BackReport.send_cstore(all_files, target_host, target_port, target_aet)

    def aspects_back_report(self, method, target_host, target_port, target_aet, value_map, series_desc_list=None):
        ncct_can_send_summary = value_map.get("ncct_can_send_summary")
        aspects_map = {
            "3D ASPECTS MASK USC": "3D_ASPECTS_MASK_USC",
            "3D PC-ASPECTS SUMMARY USC": "3D_PC-ASPECTS_SUMMARY_USC",
            "3D ASPECTS SUMMARY USC": "3D_ASPECTS_SUMMARY_USC",
            "HEMORRHAGE SUMMARY USC": "HEMORRHAGE_SUMMARY_USC",
            "HEMORRHAGE MASK USC": "HEMORRHAGE_MASK_USC",
        }
        if not series_desc_list:
            aspects_dict = aspects_map
        else:
            aspects_dict = {}
            for series_desc, path_name in aspects_map.items():
                if series_desc in series_desc_list:
                    aspects_dict.setdefault(series_desc, path_name)

        log.debug(f"aspects_dict: {aspects_dict}")
        resources = []
        all_files = []
        if not ncct_can_send_summary and not series_desc_list:
            return True
        for series_description, dir_name in aspects_dict.items():
            series_qs = Series.objects.filter(
                study__study_instance_uid=self.study_instance_uid,
                original_series=self.series_instance_uid,
                series_description=series_description)
            if not series_qs.exists():
                log.debug(f"BackReport > series not exists: series_description: {series_description}, path:{dir_name}")
                continue
            series = series_qs.first()
            series_uid = series.series_instance_uid
            base_dir = "/data/ctpdata" if os.path.exists("/data/ctpdata") else "/code/data"
            dcm_path = f"{base_dir}/static/{self.study_instance_uid}/{self.series_instance_uid}/{dir_name}/dcm/"

            log.debug(f"BackReport > dcm_path: {dcm_path}")
            # c_move
            files = FileUtils.get_all_file(dcm_path)
            all_files.extend(files)
            if method == "orthanc":
                for file in files:
                    upload_result = BackReport.upload_image(file)
                    if not upload_result:
                        log.debug("upload_image error")
                        return upload_result
                resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
                concatenation = "{}|{}|{}".format(series.study.patient_id, self.study_instance_uid, series_uid)
                series.orthanc_id = self.get_orthanc_id(concatenation)
                series.save()
        if method == "orthanc":
            data = {"Level": "SERIES", "TargetAet": target_aet, "Timeout": 60, "Resources": resources}
            return BackReport.move(data)
        return BackReport.send_cstore(all_files, target_host, target_port, target_aet)

    def send(self, series_desc_list=None):
        code_map = {
            Const.ALGORITHM_TYPE_CTA: "47006",
            Const.ALGORITHM_TYPE_CTP: "45001",
            Const.ALGORITHM_TYPE_ASPECTS: "46001",
        }
        code = code_map.get(self.algorithm_type, "")
        system_config = SystemConfig.objects.filter(code="reportBackMethod")
        if not system_config.exists():
            log.debug("BackReport > reportBackMethod not exists")
            return code
        method = system_config.first().value

        target_aet, target_host, target_port = BackReport.get_target_aet()
        if not all([target_aet, target_host, target_port]):
            log.debug(f"BackReport > pacs server not found: {target_aet, target_host, target_port}")
            return code
        value_map = BackReport.get_algorithm_report_value()
        result = True
        if self.algorithm_type in [Const.ALGORITHM_TYPE_CTA, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_ASPECTS]:
            try:
                log.info("BackReport > send by {}".format(method))
                result = getattr(self, f"{self.algorithm_type}_back_report")(method, target_host, target_port,
                                                                             target_aet, value_map, series_desc_list)
            except:
                log.error(f"BackReport > error :{traceback.format_exc()}")
                result = False
        if not result:
            return code


class FileUtils:

    @staticmethod
    def get_one(dir_path):
        """
        获取目录下任意一个文件

        :param dir_path: 目录路径
        :return:
        """
        for root, dirs, files in os.walk(dir_path):
            if not files:
                return None
            for file in files:
                return os.path.join(dir_path, file)

    @staticmethod
    def get_filename_without_suffix(path):
        return os.path.splitext(path)[0]

    @staticmethod
    def get_all_file(path, suffix=""):
        """
        获取指定目录下的所有文件

        :param suffix:
        :param path: 文件路径
        :return: 文件列表
        """
        file_list = []
        sub_dir_list = []
        if os.path.isfile(path):
            file_list.append(path)
            return file_list
        sub_dir_list.append(path)
        while len(sub_dir_list):
            dir_path = sub_dir_list.pop()
            for name in sorted(os.listdir(dir_path)):
                file_path = os.path.join(dir_path, name)
                if os.path.isdir(file_path):
                    sub_dir_list.append(file_path)
                    continue
                if not suffix or (suffix and name.endswith(suffix)):
                    file_list.append(file_path)
        return file_list


if __name__ == '__main__':
    # BackReport(study_instance_uid='1.2.826.0.1.3680043.8.498.27719868952950067693818144521151132737',
    #          series_instance_uid='1.2.826.0.1.3680043.8.498.29872767099220863066679719626856603082',
    #          algorithm_type='cta').send()

    # BackReport(study_instance_uid='1.2.826.0.1.3680043.8.498.67647188014487190914844395682535283431',
    #          series_instance_uid='1.2.826.0.1.3680043.8.498.11611244739808009084713111061013734367',
    #          algorithm_type='ctp').send()

    BackReport(study_instance_uid='1.2.826.0.1.3680043.8.498.23076095815894445812527492780444759961',
               series_instance_uid='1.2.826.0.1.3680043.8.498.11771082772982136579250031478014202456',
               algorithm_type='aspects').send()
