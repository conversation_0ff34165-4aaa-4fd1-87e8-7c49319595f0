import datetime
import json
import re
import uuid

from django.db import models

# Create your models here.

pattern = re.compile(r'(?<!^)(?=[A-Z])')


def camel_to_snake(str):
    return pattern.sub('_', str).lower()


class PDFReport(models.Model):
    """
    PDF 报告信息
    """
    id = models.CharField(primary_key=True, verbose_name='uuid', max_length=255)
    study_id = models.CharField(max_length=255, verbose_name="检查ID")
    study_instance_uid = models.CharField(verbose_name='study instance id', max_length=255, null=True)
    patient_name = models.Char<PERSON><PERSON>(verbose_name='患者姓名id', max_length=255, null=True)
    male = models.Char<PERSON><PERSON>(verbose_name="性别", null=True, max_length=255)
    age = models.Char<PERSON><PERSON>(verbose_name="年龄", max_length=255)
    check_no = models.Char<PERSON>ield(max_length=255, verbose_name="检查号", null=True)
    image_no = models.Char<PERSON>ield(max_length=255, verbose_name="影像号")
    ctp_report_text = models.TextField(verbose_name="ctp报告信息", null=True)
    ctp_image = models.TextField(max_length=1024, default="", verbose_name="CTP报告图片")
    ncct_report_text = models.TextField(verbose_name="ncct报告信息", null=True)
    ncct_image = models.TextField(max_length=1024, default="", verbose_name="NCCT报告图片")
    cta_report_text = models.TextField(verbose_name="cta报告信息", null=True)
    cta_image = models.TextField(max_length=1024, default="", verbose_name="CTA报告图片")
    check_datetime = models.CharField(verbose_name="检查时间", null=True, max_length=64)
    report_date = models.CharField(verbose_name="报告时间", null=True, max_length=32)
    report_doctor = models.CharField(verbose_name="报告医生", null=True, max_length=32)
    audit_doctor = models.CharField(verbose_name="审核医生", null=True, max_length=32)
    conclusion = models.CharField(verbose_name="印象", null=True, max_length=255)
    ctp_conclusion = models.CharField(verbose_name="CTP影像诊断意见", null=True, max_length=255)
    ncct_conclusion = models.CharField(verbose_name="NCCT影像诊断意见", null=True, max_length=255)
    cta_conclusion = models.CharField(verbose_name="CTA影像诊断意见", null=True, max_length=255)
    create_datetime = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="创建时间")

    class Meta:
        db_table = 't_pdf_report'
        verbose_name = 'PDF 报告信息'
        verbose_name_plural = 'PDF 报告信息'

    @staticmethod
    def create_from_json(json_data):
        pdf_report = PDFReport()
        setattr(pdf_report, "id", uuid.uuid1())
        setattr(pdf_report, "conclusion", "")
        # 不清楚为啥auto_now_add 和 auto_new 在个别数据库没有效果
        setattr(pdf_report, "create_datetime", datetime.datetime.now())
        setattr(pdf_report, "update_time", datetime.datetime.now())
        for key in json_data:
            value = json_data[key]
            if key in ["ncctImage", "ctpImage", "ctaImage"]:
                value = json.dumps(value)
            setattr(pdf_report, camel_to_snake(key), value)
        return pdf_report
